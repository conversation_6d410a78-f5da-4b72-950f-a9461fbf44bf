# Kotlin 协程高级用法

## 1. Flow 数据流

### 1.1 Flow 基础

**通俗解释**：Flow 就像是"数据的水流"，可以持续发送多个值，而不像挂起函数只能返回一个值。

```kotlin
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.*

// 创建 Flow
fun createFlow(): Flow<Int> = flow {
    for (i in 1..5) {
        delay(1000)
        emit(i) // 发送数据
    }
}

// 使用 Flow
suspend fun flowBasicDemo() {
    createFlow().collect { value ->
        println("Received: $value")
    }
}
```

### 1.2 Flow 构建器

```kotlin
suspend fun flowBuildersDemo() {
    // flowOf - 固定值
    val fixedFlow = flowOf(1, 2, 3, 4, 5)
    
    // asFlow - 从集合创建
    val listFlow = listOf(1, 2, 3).asFlow()
    
    // flow 构建器
    val customFlow = flow {
        emit(1)
        delay(1000)
        emit(2)
        delay(1000)
        emit(3)
    }
    
    // 冷流 - 每次收集都重新开始
    println("First collection:")
    customFlow.collect { println(it) }
    
    println("Second collection:")
    customFlow.collect { println(it) }
}
```

### 1.3 Flow 操作符

```kotlin
suspend fun flowOperatorsDemo() {
    val numbersFlow = (1..10).asFlow()
    
    // 转换操作符
    numbersFlow
        .map { it * it }           // 平方
        .filter { it > 10 }        // 过滤
        .take(3)                   // 取前3个
        .collect { println("Square > 10: $it") }
    
    // 组合操作符
    val flow1 = flowOf(1, 2, 3)
    val flow2 = flowOf("A", "B", "C")
    
    flow1.zip(flow2) { num, letter ->
        "$num$letter"
    }.collect { println("Zipped: $it") }
    
    // 异常处理
    flow {
        emit(1)
        emit(2)
        throw RuntimeException("Error!")
        emit(3)
    }.catch { e ->
        println("Caught: ${e.message}")
        emit(-1) // 发送默认值
    }.collect { println("Value: $it") }
}
```

## 2. Channel 通道

### 2.1 Channel 基础

**通俗解释**：Channel 就像是"邮箱"，一个协程可以往里面放信件，另一个协程可以从里面取信件。

```kotlin
import kotlinx.coroutines.channels.*

suspend fun channelBasicDemo() {
    val channel = Channel<Int>()
    
    // 生产者协程
    launch {
        for (i in 1..5) {
            delay(1000)
            channel.send(i)
            println("Sent: $i")
        }
        channel.close() // 关闭通道
    }
    
    // 消费者协程
    launch {
        for (value in channel) {
            println("Received: $value")
        }
    }
}
```

### 2.2 Channel 类型

```kotlin
suspend fun channelTypesDemo() {
    // 无缓冲通道 - 必须有接收者才能发送
    val unbuffered = Channel<Int>()
    
    // 有缓冲通道 - 可以缓存指定数量的元素
    val buffered = Channel<Int>(capacity = 3)
    
    // 无限缓冲通道
    val unlimited = Channel<Int>(Channel.UNLIMITED)
    
    // 冲突通道 - 新值会覆盖旧值
    val conflated = Channel<Int>(Channel.CONFLATED)
    
    // 使用 produce 构建器
    val producer = produce {
        repeat(5) { i ->
            send(i)
            delay(1000)
        }
    }
    
    producer.consumeEach { value ->
        println("Produced: $value")
    }
}
```

### 2.3 多生产者多消费者

```kotlin
suspend fun multiProducerConsumerDemo() {
    val channel = Channel<String>()
    
    // 多个生产者
    repeat(3) { producerId ->
        launch {
            repeat(3) { messageId ->
                val message = "Producer-$producerId: Message-$messageId"
                channel.send(message)
                delay(1000)
            }
        }
    }
    
    // 多个消费者
    repeat(2) { consumerId ->
        launch {
            for (message in channel) {
                println("Consumer-$consumerId received: $message")
            }
        }
    }
    
    delay(10000)
    channel.close()
}
```

## 3. 协程上下文和调度器

### 3.1 协程上下文组合

```kotlin
suspend fun contextCompositionDemo() {
    val context = Dispatchers.IO + 
                 CoroutineName("MyCoroutine") + 
                 SupervisorJob()
    
    withContext(context) {
        println("Running in: ${coroutineContext[CoroutineName]}")
        println("Job: ${coroutineContext[Job]}")
        println("Dispatcher: ${coroutineContext[ContinuationInterceptor]}")
    }
}
```

### 3.2 自定义调度器

```kotlin
import java.util.concurrent.Executors

fun customDispatcherDemo() {
    // 自定义线程池调度器
    val customDispatcher = Executors.newFixedThreadPool(4).asCoroutineDispatcher()
    
    runBlocking {
        launch(customDispatcher) {
            println("Running on custom dispatcher: ${Thread.currentThread().name}")
        }
    }
    
    customDispatcher.close() // 记得关闭
}
```

## 4. 结构化并发进阶

### 4.1 coroutineScope 和 supervisorScope

```kotlin
suspend fun structuredConcurrencyDemo() {
    try {
        coroutineScope {
            launch {
                delay(1000)
                println("Child 1 completed")
            }
            
            launch {
                delay(500)
                throw RuntimeException("Child 2 failed")
            }
            
            launch {
                delay(1500)
                println("Child 3 completed") // 不会执行
            }
        }
    } catch (e: Exception) {
        println("Caught: ${e.message}")
    }
    
    // supervisorScope - 子协程失败不影响其他子协程
    supervisorScope {
        launch {
            delay(1000)
            println("Supervisor child 1 completed")
        }
        
        launch {
            delay(500)
            throw RuntimeException("Supervisor child 2 failed")
        }
        
        launch {
            delay(1500)
            println("Supervisor child 3 completed") // 会执行
        }
    }
}
```

### 4.2 Job 层次结构

```kotlin
fun jobHierarchyDemo() {
    runBlocking {
        val parentJob = Job()
        val scope = CoroutineScope(Dispatchers.Default + parentJob)
        
        val child1 = scope.launch {
            delay(2000)
            println("Child 1 completed")
        }
        
        val child2 = scope.launch {
            delay(3000)
            println("Child 2 completed")
        }
        
        delay(1000)
        println("Cancelling parent job")
        parentJob.cancel() // 取消所有子协程
        
        child1.join()
        child2.join()
        
        println("All children cancelled")
    }
}
```

## 5. 协程测试

### 5.1 测试挂起函数

```kotlin
import kotlinx.coroutines.test.*

class UserService {
    suspend fun fetchUser(id: String): String {
        delay(1000)
        return "User-$id"
    }
}

@Test
fun testSuspendFunction() = runTest {
    val service = UserService()
    val result = service.fetchUser("123")
    assertEquals("User-123", result)
}
```

### 5.2 测试时间相关的协程

```kotlin
@Test
fun testTimeBasedCoroutine() = runTest {
    val startTime = currentTime
    
    launch {
        delay(1000)
        println("Task completed")
    }
    
    advanceTimeBy(1000) // 快进时间
    
    val endTime = currentTime
    assertEquals(1000, endTime - startTime)
}
```

## 6. 协程性能优化

### 6.1 避免不必要的协程创建

```kotlin
// ❌ 错误：为每个元素创建协程
suspend fun processItemsBad(items: List<String>) {
    items.forEach { item ->
        launch {
            processItem(item)
        }
    }
}

// ✅ 正确：批量处理
suspend fun processItemsGood(items: List<String>) {
    items.chunked(100).forEach { chunk ->
        launch {
            chunk.forEach { item ->
                processItem(item)
            }
        }
    }
}

suspend fun processItem(item: String) {
    delay(10)
    println("Processed: $item")
}
```

### 6.2 合理使用调度器

```kotlin
class DataProcessor {
    suspend fun processData(data: List<String>): List<String> {
        return withContext(Dispatchers.Default) {
            // CPU 密集型任务使用 Default
            data.map { item ->
                heavyComputation(item)
            }
        }
    }
    
    suspend fun saveToDatabase(data: List<String>) {
        withContext(Dispatchers.IO) {
            // I/O 操作使用 IO
            data.forEach { item ->
                database.save(item)
            }
        }
    }
    
    private fun heavyComputation(item: String): String {
        // 模拟 CPU 密集型计算
        return item.uppercase()
    }
}
```

## 7. 协程调试

### 7.1 协程调试技巧

```kotlin
// 启用协程调试
// JVM 参数：-Dkotlinx.coroutines.debug

suspend fun debuggingDemo() {
    val job = launch(CoroutineName("MyCoroutine")) {
        println("Coroutine name: ${coroutineContext[CoroutineName]}")
        delay(1000)
        println("Coroutine completed")
    }
    
    // 打印协程信息
    println("Job: $job")
    println("Is active: ${job.isActive}")
    
    job.join()
}
```

### 7.2 协程异常调试

```kotlin
val exceptionHandler = CoroutineExceptionHandler { context, exception ->
    println("Coroutine ${context[CoroutineName]} failed with $exception")
    exception.printStackTrace()
}

fun exceptionDebuggingDemo() {
    runBlocking {
        launch(exceptionHandler + CoroutineName("FailingCoroutine")) {
            throw RuntimeException("Something went wrong")
        }
        
        delay(1000)
    }
}
```

## 8. 实际应用场景

### 8.1 网络请求重试机制

```kotlin
suspend fun <T> retryWithBackoff(
    maxRetries: Int = 3,
    initialDelay: Long = 1000,
    factor: Double = 2.0,
    block: suspend () -> T
): T {
    var currentDelay = initialDelay
    repeat(maxRetries - 1) {
        try {
            return block()
        } catch (e: Exception) {
            println("Attempt failed, retrying in ${currentDelay}ms")
            delay(currentDelay)
            currentDelay = (currentDelay * factor).toLong()
        }
    }
    return block() // 最后一次尝试
}

// 使用示例
suspend fun fetchDataWithRetry(): String {
    return retryWithBackoff {
        // 模拟可能失败的网络请求
        if (Random.nextBoolean()) {
            throw IOException("Network error")
        }
        "Success data"
    }
}
```

### 8.2 并发限制

```kotlin
import kotlinx.coroutines.sync.Semaphore

class ConcurrencyLimiter(maxConcurrency: Int) {
    private val semaphore = Semaphore(maxConcurrency)
    
    suspend fun <T> execute(block: suspend () -> T): T {
        semaphore.acquire()
        try {
            return block()
        } finally {
            semaphore.release()
        }
    }
}

// 使用示例
suspend fun processWithLimit() {
    val limiter = ConcurrencyLimiter(3) // 最多3个并发
    
    val jobs = (1..10).map { id ->
        async {
            limiter.execute {
                println("Processing $id")
                delay(1000)
                "Result $id"
            }
        }
    }
    
    val results = jobs.awaitAll()
    println("All results: $results")
}
```

## 9. 面试重点

### 高级面试问题：

1. **Flow 和 Channel 的区别？**
   - Flow 是冷流，Channel 是热流
   - Flow 每次收集都重新开始，Channel 是一次性的

2. **如何处理协程中的背压？**
   - 使用 buffer、conflate、collectLatest 等操作符

3. **协程的内存泄漏如何避免？**
   - 使用结构化并发、正确的作用域管理、及时取消

4. **如何测试协程代码？**
   - 使用 runTest、TestCoroutineDispatcher、advanceTimeBy

5. **协程的性能优化策略？**
   - 合理选择调度器、避免过度创建协程、使用 Channel 缓冲

### 最佳实践：
- 使用结构化并发管理协程生命周期
- 合理选择 Flow 和 Channel
- 正确处理异常和取消
- 使用适当的调度器
- 编写可测试的协程代码
