# Kotlin 协程基础

## 1. 什么是协程？

### 通俗解释
协程就像是"轻量级的线程"，想象一下：
- **传统线程**：就像是重型卡车，启动慢、占用资源多，但能处理重活
- **协程**：就像是自行车，启动快、占用资源少，灵活机动

### 专业解释
协程（Coroutine）是一种并发设计模式，可以在单个线程上执行多个任务，通过协作式多任务来实现异步编程。

### 协程的优势
- **轻量级**：创建成千上万个协程不会导致内存溢出
- **结构化并发**：避免内存泄漏和资源浪费
- **取消支持**：可以取消整个协程树
- **异常处理**：结构化的异常传播

## 2. 协程基础概念

### 2.1 挂起函数（Suspend Function）

```kotlin
// 挂起函数必须用 suspend 关键字标记
suspend fun fetchUserData(userId: String): User {
    delay(1000) // 模拟网络请求，挂起 1 秒
    return User(userId, "<PERSON> Doe")
}

// 挂起函数只能在协程或其他挂起函数中调用
suspend fun processUser(userId: String) {
    val user = fetchUserData(userId) // 这里会挂起
    println("User: ${user.name}")
}
```

**通俗解释**：挂起函数就像是"可以暂停的函数"，当遇到耗时操作时，它会"让出"线程给其他任务，等操作完成后再"回来"继续执行。

### 2.2 协程构建器

```kotlin
import kotlinx.coroutines.*

fun main() {
    // runBlocking - 阻塞当前线程
    runBlocking {
        println("Hello from runBlocking")
        delay(1000)
        println("World!")
    }
    
    // launch - 启动新协程（不阻塞）
    GlobalScope.launch {
        delay(1000)
        println("Hello from launch")
    }
    
    // async - 启动新协程并返回 Deferred
    val deferred = GlobalScope.async {
        delay(1000)
        "Hello from async"
    }
    
    // 等待结果
    runBlocking {
        val result = deferred.await()
        println(result)
    }
}
```

### 2.3 协程作用域（CoroutineScope）

```kotlin
class MyActivity : CoroutineScope {
    private val job = Job()
    override val coroutineContext = Dispatchers.Main + job
    
    fun doSomething() {
        // 在 Activity 的作用域中启动协程
        launch {
            val data = withContext(Dispatchers.IO) {
                fetchDataFromNetwork()
            }
            updateUI(data)
        }
    }
    
    fun onDestroy() {
        job.cancel() // 取消所有协程
    }
}
```

**通俗解释**：协程作用域就像是"协程的管理员"，负责管理协程的生命周期，当作用域被销毁时，所有的协程都会被取消。

## 3. 协程调度器（Dispatchers）

```kotlin
// Main - 主线程（UI 线程）
launch(Dispatchers.Main) {
    updateUI() // 更新 UI
}

// IO - I/O 操作优化的线程池
launch(Dispatchers.IO) {
    val data = readFromDatabase() // 数据库操作
}

// Default - CPU 密集型任务的线程池
launch(Dispatchers.Default) {
    val result = heavyComputation() // 复杂计算
}

// Unconfined - 不限制在特定线程
launch(Dispatchers.Unconfined) {
    // 在调用者线程启动，但挂起后可能在其他线程恢复
}
```

**通俗解释**：调度器就像是"任务分配员"，根据任务类型分配到合适的线程池执行。

## 4. 协程上下文切换

```kotlin
suspend fun loadUserProfile(userId: String) {
    // 在 IO 线程执行网络请求
    val user = withContext(Dispatchers.IO) {
        apiService.getUser(userId)
    }
    
    // 在 Default 线程执行数据处理
    val processedData = withContext(Dispatchers.Default) {
        processUserData(user)
    }
    
    // 回到 Main 线程更新 UI
    withContext(Dispatchers.Main) {
        updateUserUI(processedData)
    }
}
```

## 5. 协程的取消

```kotlin
fun main() = runBlocking {
    val job = launch {
        repeat(1000) { i ->
            println("Job: I'm sleeping $i ...")
            delay(500L)
        }
    }
    
    delay(1300L) // 等待一段时间
    println("I'm tired of waiting!")
    job.cancel() // 取消协程
    job.join() // 等待协程完成取消
    println("Now I can quit.")
}

// 检查取消状态
suspend fun doWork() {
    repeat(1000) { i ->
        if (!isActive) { // 检查协程是否仍然活跃
            return
        }
        // 执行工作
        println("Working $i")
        delay(100)
    }
}
```

## 6. 异常处理

```kotlin
// 使用 try-catch
suspend fun handleExceptions() {
    try {
        val result = riskyOperation()
        println("Success: $result")
    } catch (e: Exception) {
        println("Error: ${e.message}")
    }
}

// 使用 CoroutineExceptionHandler
val handler = CoroutineExceptionHandler { _, exception ->
    println("Caught $exception")
}

val job = GlobalScope.launch(handler) {
    throw AssertionError()
}
```

## 7. 协程与回调对比

### 传统回调方式
```kotlin
// 回调地狱
fun loadUserData(userId: String, callback: (User?) -> Unit) {
    loadUser(userId) { user ->
        if (user != null) {
            loadUserPosts(user.id) { posts ->
                if (posts != null) {
                    loadPostComments(posts[0].id) { comments ->
                        // 嵌套越来越深...
                        callback(user)
                    }
                } else {
                    callback(null)
                }
            }
        } else {
            callback(null)
        }
    }
}
```

### 协程方式
```kotlin
// 线性代码，易于理解
suspend fun loadUserData(userId: String): User? {
    return try {
        val user = loadUser(userId)
        val posts = loadUserPosts(user.id)
        val comments = loadPostComments(posts[0].id)
        user
    } catch (e: Exception) {
        null
    }
}
```

## 8. 常见使用场景

### 8.1 网络请求
```kotlin
class UserRepository {
    suspend fun getUser(id: String): User {
        return withContext(Dispatchers.IO) {
            apiService.getUser(id)
        }
    }
}

class UserViewModel : ViewModel() {
    fun loadUser(id: String) {
        viewModelScope.launch {
            try {
                val user = userRepository.getUser(id)
                _userLiveData.value = user
            } catch (e: Exception) {
                _errorLiveData.value = e.message
            }
        }
    }
}
```

### 8.2 并发执行
```kotlin
suspend fun loadDashboardData(): DashboardData {
    return coroutineScope {
        // 并发执行多个请求
        val userDeferred = async { loadUser() }
        val postsDeferred = async { loadPosts() }
        val notificationsDeferred = async { loadNotifications() }
        
        // 等待所有结果
        DashboardData(
            user = userDeferred.await(),
            posts = postsDeferred.await(),
            notifications = notificationsDeferred.await()
        )
    }
}
```

## 9. 面试重点

### 常见面试问题：

1. **协程和线程的区别？**
   - 协程是轻量级的，线程是重量级的
   - 协程在用户态调度，线程在内核态调度
   - 协程支持结构化并发

2. **什么是挂起函数？**
   - 可以暂停执行而不阻塞线程的函数
   - 只能在协程或其他挂起函数中调用

3. **协程如何避免回调地狱？**
   - 通过挂起函数将异步代码写成同步形式
   - 保持代码的线性结构

4. **协程的取消机制？**
   - 协作式取消，需要检查 isActive
   - 结构化并发，父协程取消会取消所有子协程

### 最佳实践：
- 使用结构化并发
- 正确处理协程取消
- 选择合适的调度器
- 避免使用 GlobalScope
- 合理使用 async 和 launch
