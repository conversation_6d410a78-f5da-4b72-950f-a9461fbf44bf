# Kotlin 知识体系

本目录包含 Kotlin 相关的知识点整理，涵盖面试常见问题、协程原理、实战示例等内容。

## 目录结构

### 📚 基础知识

- [01_Kotlin_Basics.md](./01_Kotlin_Basics.md) - Kotlin 基础语法和特性
- [02_Kotlin_OOP.md](./02_Kotlin_OOP.md) - Kotlin 面向对象编程
- [03_Kotlin_Functional.md](./03_Kotlin_Functional.md) - Kotlin 函数式编程

### 🔥 协程专题
- [04_Coroutines_Basics.md](./04_Coroutines_Basics.md) - 协程基础概念
- [05_Coroutines_Principles.md](./05_Coroutines_Principles.md) - 协程原理深入解析
- [06_Coroutines_Advanced.md](./06_Coroutines_Advanced.md) - 协程高级用法

### 💼 面试专题
- [07_Interview_Questions.md](./07_Interview_Questions.md) - Kotlin 面试常见问题
- [08_Interview_Coroutines.md](./08_Interview_Coroutines.md) - 协程相关面试题

### 🛠️ 实战示例
- [demos/](./demos/) - 各种示例代码（Markdown格式展示）
  - [BasicDemo.md](./demos/BasicDemo.md) - 基础语法示例
  - [CoroutinesDemo.md](./demos/CoroutinesDemo.md) - 协程使用示例
  - [ComparisonDemo.md](./demos/ComparisonDemo.md) - 与Java对比示例

## 学习路径建议

### 初学者
1. 先学习 Kotlin 基础语法 (01-03)
2. 理解协程基础概念 (04)
3. 通过示例代码实践

### 进阶学习
1. 深入学习协程原理 (05-06)
2. 准备面试相关内容 (07-08)
3. 实战项目应用

### 面试准备
1. 重点复习面试专题 (07-08)
2. 熟练掌握协程原理和使用
3. 能够对比 Kotlin 与 Java 的差异

## 特色内容

- ✅ **通俗易懂**：每个概念都有简单的类比解释
- ✅ **专业深入**：提供技术原理的深度分析
- ✅ **实战导向**：丰富的代码示例和最佳实践
- ✅ **面试友好**：针对面试场景的问题整理
- ✅ **对比学习**：与Java等语言的对比分析

## 快速开始

如果你是第一次学习 Kotlin，建议从 [01_Kotlin_Basics.md](./01_Kotlin_Basics.md) 开始。

如果你想了解协程，可以直接查看 [04_Coroutines_Basics.md](./04_Coroutines_Basics.md)。

如果你在准备面试，重点关注 [07_Interview_Questions.md](./07_Interview_Questions.md) 和 [08_Interview_Coroutines.md](./08_Interview_Coroutines.md)。
