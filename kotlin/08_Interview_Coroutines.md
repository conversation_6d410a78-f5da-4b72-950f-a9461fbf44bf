# Kotlin 协程面试专题

## 1. 基础概念类

### Q1: 什么是协程？与线程有什么区别？

**标准答案**：
协程是一种并发设计模式，可以在单个线程上执行多个任务。

**详细对比**：

| 特性 | 线程 | 协程 |
|------|------|------|
| 重量级程度 | 重量级（1MB栈空间） | 轻量级（几KB） |
| 创建开销 | 毫秒级 | 纳秒级 |
| 调度方式 | 抢占式（内核调度） | 协作式（用户态调度） |
| 上下文切换 | 微秒级 | 纳秒级 |
| 最大数量 | 几千个 | 几十万个 |
| 内存占用 | 固定栈大小 | 按需分配 |

**代码示例**：
```kotlin
// 创建 10万个协程（轻松完成）
fun main() = runBlocking {
    repeat(100_000) {
        launch {
            delay(1000)
            println("Coroutine $it")
        }
    }
}

// 创建 10万个线程（会导致 OOM）
fun main() {
    repeat(100_000) {
        Thread {
            Thread.sleep(1000)
            println("Thread $it")
        }.start()
    }
}
```

### Q2: 什么是挂起函数？为什么只能在协程中调用？

**答案**：
挂起函数是可以暂停执行而不阻塞线程的函数，用 `suspend` 关键字标记。

**原理解释**：
```kotlin
// 挂起函数的真实签名
suspend fun delay(timeMillis: Long): Unit
// 编译器转换为：
fun delay(timeMillis: Long, continuation: Continuation<Unit>): Any?

// 只能在协程中调用的原因：需要 Continuation 参数
fun main() {
    // delay(1000) // ❌ 编译错误：缺少 Continuation 参数
    
    runBlocking {
        delay(1000) // ✅ runBlocking 提供了 Continuation
    }
}
```

### Q3: 协程是如何实现挂起和恢复的？

**答案**：通过状态机和 Continuation 机制。

**详细原理**：
```kotlin
// 原始代码
suspend fun example() {
    println("Before")
    delay(1000)
    println("After")
}

// 编译器生成的状态机（简化版）
fun example(continuation: Continuation<Unit>): Any? {
    val sm = continuation as? ExampleStateMachine 
        ?: ExampleStateMachine(continuation)
    
    when (sm.label) {
        0 -> {
            println("Before")
            sm.label = 1
            return delay(1000, sm) // 返回 COROUTINE_SUSPENDED
        }
        1 -> {
            println("After")
            return Unit
        }
        else -> throw IllegalStateException()
    }
}

class ExampleStateMachine(
    private val completion: Continuation<Unit>
) : Continuation<Unit> {
    var label = 0
    
    override fun resumeWith(result: Result<Unit>) {
        example(this) // 恢复执行
    }
}
```

## 2. 协程构建器类

### Q4: launch、async、runBlocking 的区别？

**答案**：

| 构建器 | 返回值 | 阻塞性 | 使用场景 |
|--------|--------|--------|----------|
| launch | Job | 非阻塞 | 启动协程，不需要返回值 |
| async | Deferred<T> | 非阻塞 | 并发执行，需要返回值 |
| runBlocking | T | 阻塞 | 测试、main函数 |

**代码示例**：
```kotlin
fun main() = runBlocking {
    // launch - 启动协程，不关心返回值
    val job = launch {
        delay(1000)
        println("Launch completed")
    }
    
    // async - 并发执行，获取返回值
    val deferred = async {
        delay(1000)
        "Async result"
    }
    
    job.join() // 等待完成
    val result = deferred.await() // 获取结果
    println(result)
}

// runBlocking - 阻塞当前线程
fun main() {
    runBlocking {
        delay(1000)
        println("RunBlocking completed")
    }
    println("This prints after runBlocking")
}
```

### Q5: 什么时候使用 async，什么时候使用 launch？

**答案**：
- **async**：需要返回值的并发操作
- **launch**：不需要返回值的异步操作

**最佳实践**：
```kotlin
// ✅ 正确使用 async - 并发获取数据
suspend fun loadDashboard(): DashboardData {
    return coroutineScope {
        val userDeferred = async { loadUser() }
        val postsDeferred = async { loadPosts() }
        val notificationsDeferred = async { loadNotifications() }
        
        DashboardData(
            user = userDeferred.await(),
            posts = postsDeferred.await(),
            notifications = notificationsDeferred.await()
        )
    }
}

// ❌ 错误使用 async - 顺序执行
suspend fun wrongUsage() {
    val user = async { loadUser() }.await() // 没有并发优势
    val posts = async { loadPosts() }.await()
}

// ✅ 正确使用 launch - 启动后台任务
fun startBackgroundTasks() {
    GlobalScope.launch {
        cleanupTempFiles()
    }
    
    GlobalScope.launch {
        syncDataToServer()
    }
}
```

## 3. 协程作用域类

### Q6: 什么是协程作用域？为什么需要它？

**答案**：
协程作用域定义了协程的生命周期，提供结构化并发，防止内存泄漏。

**问题演示**：
```kotlin
// ❌ 错误做法 - 可能导致内存泄漏
class MainActivity : AppCompatActivity() {
    fun loadData() {
        GlobalScope.launch {
            val data = fetchDataFromNetwork()
            updateUI(data) // Activity 可能已经销毁
        }
    }
}

// ✅ 正确做法 - 使用生命周期感知的作用域
class MainActivity : AppCompatActivity() {
    fun loadData() {
        lifecycleScope.launch {
            val data = fetchDataFromNetwork()
            updateUI(data) // Activity 销毁时协程自动取消
        }
    }
}
```

### Q7: GlobalScope 有什么问题？应该如何替代？

**答案**：
GlobalScope 的问题：
1. 生命周期与应用程序相同，容易内存泄漏
2. 难以测试和控制
3. 不支持结构化并发

**替代方案**：
```kotlin
// Android 中的替代方案
class MyActivity : AppCompatActivity() {
    // 1. lifecycleScope - 与生命周期绑定
    fun method1() {
        lifecycleScope.launch { /* ... */ }
    }
    
    // 2. viewModelScope - 与 ViewModel 绑定
    class MyViewModel : ViewModel() {
        fun loadData() {
            viewModelScope.launch { /* ... */ }
        }
    }
}

// 自定义作用域
class MyRepository {
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    fun fetchData() {
        scope.launch { /* ... */ }
    }
    
    fun cleanup() {
        scope.cancel() // 清理所有协程
    }
}
```

## 4. 调度器类

### Q8: 协程调度器有哪些？分别用于什么场景？

**答案**：

| 调度器 | 线程池 | 使用场景 | 示例 |
|--------|--------|----------|------|
| Dispatchers.Main | 主线程 | UI 更新 | 更新界面 |
| Dispatchers.IO | I/O 优化线程池 | 网络、文件操作 | 数据库、网络请求 |
| Dispatchers.Default | CPU 优化线程池 | CPU 密集型任务 | 图像处理、算法计算 |
| Dispatchers.Unconfined | 不限制 | 特殊场景 | 测试 |

**代码示例**：
```kotlin
class UserRepository {
    suspend fun loadUser(id: String): User {
        // 网络请求使用 IO 调度器
        return withContext(Dispatchers.IO) {
            apiService.getUser(id)
        }
    }
    
    suspend fun processUserData(user: User): ProcessedUser {
        // CPU 密集型任务使用 Default 调度器
        return withContext(Dispatchers.Default) {
            heavyProcessing(user)
        }
    }
    
    fun updateUI(user: ProcessedUser) {
        // UI 更新使用 Main 调度器
        lifecycleScope.launch(Dispatchers.Main) {
            userNameText.text = user.name
        }
    }
}
```

### Q9: withContext 的作用是什么？

**答案**：
`withContext` 用于切换协程的执行上下文（通常是调度器），并在指定上下文中执行代码块。

**使用场景**：
```kotlin
suspend fun loadAndProcessData(): String {
    // 在 IO 线程执行网络请求
    val rawData = withContext(Dispatchers.IO) {
        downloadData()
    }
    
    // 在 Default 线程执行数据处理
    val processedData = withContext(Dispatchers.Default) {
        processData(rawData)
    }
    
    // 在 Main 线程更新 UI
    withContext(Dispatchers.Main) {
        updateUI(processedData)
    }
    
    return processedData
}
```

## 5. 异常处理类

### Q10: 协程中如何处理异常？

**答案**：
协程异常处理有多种方式：

1. **try-catch**
2. **CoroutineExceptionHandler**
3. **SupervisorJob**

```kotlin
// 1. try-catch
suspend fun handleWithTryCatch() {
    try {
        val result = riskyOperation()
        println("Success: $result")
    } catch (e: Exception) {
        println("Error: ${e.message}")
    }
}

// 2. CoroutineExceptionHandler
val handler = CoroutineExceptionHandler { _, exception ->
    println("Caught $exception")
}

val job = GlobalScope.launch(handler) {
    throw RuntimeException("Test exception")
}

// 3. SupervisorJob - 子协程异常不影响其他子协程
val supervisor = SupervisorJob()
val scope = CoroutineScope(Dispatchers.Default + supervisor)

scope.launch {
    throw RuntimeException("Child 1 failed")
}

scope.launch {
    delay(1000)
    println("Child 2 completed") // 仍然会执行
}
```

### Q11: launch 和 async 的异常处理有什么区别？

**答案**：

| 构建器 | 异常处理方式 | 说明 |
|--------|-------------|------|
| launch | 立即抛出异常 | 异常会传播到父协程 |
| async | 在 await() 时抛出 | 异常被封装在 Deferred 中 |

```kotlin
fun main() = runBlocking {
    // launch - 异常立即抛出
    val job = launch {
        throw RuntimeException("Launch exception")
    }
    
    // async - 异常在 await() 时抛出
    val deferred = async {
        throw RuntimeException("Async exception")
    }
    
    try {
        job.join() // 这里会抛出异常
    } catch (e: Exception) {
        println("Caught from launch: ${e.message}")
    }
    
    try {
        deferred.await() // 这里会抛出异常
    } catch (e: Exception) {
        println("Caught from async: ${e.message}")
    }
}
```

## 6. 取消机制类

### Q12: 协程如何取消？取消是如何传播的？

**答案**：
协程取消是协作式的，通过 Job.cancel() 方法触发。

**取消传播规则**：
1. 父协程取消 → 所有子协程取消
2. 子协程取消 → 不影响父协程和兄弟协程（除非使用普通 Job）

```kotlin
fun main() = runBlocking {
    val parentJob = launch {
        val child1 = launch {
            repeat(1000) { i ->
                if (!isActive) return@launch // 检查取消状态
                println("Child 1: $i")
                delay(100)
            }
        }
        
        val child2 = launch {
            repeat(1000) { i ->
                println("Child 2: $i")
                delay(150)
            }
        }
        
        delay(500)
    }
    
    delay(300)
    parentJob.cancel() // 取消父协程，所有子协程也被取消
    parentJob.join()
}
```

### Q13: 如何让协程支持取消？

**答案**：
协程取消是协作式的，需要主动检查取消状态：

```kotlin
// 方法1：使用 isActive 检查
suspend fun cooperativeCancellation() {
    repeat(1000) { i ->
        if (!isActive) {
            println("Cancelled at $i")
            return
        }
        // 执行工作
        heavyComputation()
    }
}

// 方法2：使用 ensureActive()
suspend fun ensureActiveCancellation() {
    repeat(1000) { i ->
        ensureActive() // 如果已取消则抛出 CancellationException
        heavyComputation()
    }
}

// 方法3：使用可取消的挂起函数
suspend fun suspendingCancellation() {
    repeat(1000) { i ->
        delay(1) // delay 会检查取消状态
        heavyComputation()
    }
}
```

## 7. 实际应用类

### Q14: 在 Android 中如何正确使用协程？

**答案**：
```kotlin
class UserViewModel : ViewModel() {
    private val _userLiveData = MutableLiveData<User>()
    val userLiveData: LiveData<User> = _userLiveData
    
    private val _errorLiveData = MutableLiveData<String>()
    val errorLiveData: LiveData<String> = _errorLiveData
    
    fun loadUser(userId: String) {
        viewModelScope.launch {
            try {
                _userLiveData.value = withContext(Dispatchers.IO) {
                    userRepository.getUser(userId)
                }
            } catch (e: Exception) {
                _errorLiveData.value = e.message
            }
        }
    }
}

class MainActivity : AppCompatActivity() {
    private lateinit var viewModel: UserViewModel
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 观察数据
        viewModel.userLiveData.observe(this) { user ->
            updateUI(user)
        }
        
        // 启动数据加载
        lifecycleScope.launch {
            viewModel.loadUser("123")
        }
    }
}
```

### Q15: 协程相比 RxJava 有什么优势？

**答案**：

| 特性 | 协程 | RxJava |
|------|------|--------|
| 学习曲线 | 平缓 | 陡峭 |
| 代码可读性 | 线性代码 | 链式调用 |
| 异常处理 | 标准 try-catch | 复杂的错误操作符 |
| 内存占用 | 轻量级 | 相对重量级 |
| 调试支持 | 原生支持 | 需要特殊工具 |

```kotlin
// 协程版本 - 线性代码
suspend fun loadUserProfile(userId: String): UserProfile {
    val user = userService.getUser(userId)
    val posts = postService.getUserPosts(userId)
    val followers = followService.getFollowers(userId)
    
    return UserProfile(user, posts, followers)
}

// RxJava 版本 - 复杂的操作符
fun loadUserProfile(userId: String): Single<UserProfile> {
    return Single.zip(
        userService.getUser(userId),
        postService.getUserPosts(userId),
        followService.getFollowers(userId)
    ) { user, posts, followers ->
        UserProfile(user, posts, followers)
    }
}
```

## 8. 面试技巧

### 回答要点：
1. **概念清晰**：准确理解协程的本质
2. **原理深入**：了解底层实现机制
3. **实践经验**：结合实际项目经验
4. **对比分析**：与线程、RxJava 等对比
5. **最佳实践**：展示正确的使用方式

### 常见追问：
- "协程的性能优势体现在哪里？"
- "如何调试协程？"
- "协程有什么缺点？"
- "在什么情况下不适合使用协程？"

### 加分回答：
- 提及协程的发展历史
- 讨论协程在其他语言中的实现
- 分享协程使用中遇到的坑和解决方案
- 展示对协程生态的了解（Flow、Channel 等）
