# Kotlin 与 Java 对比示例

展示 Kotlin 相对于 Java 的优势和改进

## 1. 类定义对比

### Java 版本的 Person 类（50+ 行代码）

```java
public class PersonJava {
    private String name;
    private int age;
    private String email;

    public PersonJava(String name, int age, String email) {
        this.name = name;
        this.age = age;
        this.email = email;
    }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public int getAge() { return age; }
    public void setAge(int age) { this.age = age; }

    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        PersonJava person = (PersonJava) obj;
        return age == person.age &&
               Objects.equals(name, person.name) &&
               Objects.equals(email, person.email);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, age, email);
    }

    @Override
    public String toString() {
        return "PersonJava{" +
                "name='" + name + '\'' +
                ", age=" + age +
                ", email='" + email + '\'' +
                '}';
    }
}
```

### Kotlin 版本的 Person 类（1 行代码！）

```kotlin
data class PersonKotlin(var name: String, var age: Int, val email: String)

fun classComparisonDemo() {
    println("=== 类定义对比 ===")

    // Kotlin 数据类自动提供：
    // - 构造函数
    // - getter/setter
    // - equals/hashCode
    // - toString
    // - copy 方法

    val person1 = PersonKotlin("Alice", 25, "<EMAIL>")
    val person2 = person1.copy(name = "Bob")

    println("Person1: $person1")
    println("Person2: $person2")
    println("Are equal: ${person1 == person2}")

    // Java 需要 50+ 行代码，Kotlin 只需要 1 行！
}
```

## 2. 空安全对比

### Java 版本 - 容易出现 NPE

```java
public class UserServiceJava {
    public String getUserName(User user) {
        if (user != null) {
            String name = user.getName();
            if (name != null) {
                return name.toUpperCase();
            }
        }
        return "Unknown";
    }

    public int getNameLength(User user) {
        if (user != null) {
            String name = user.getName();
            if (name != null) {
                return name.length();
            }
        }
        return 0;
    }
}
```

### Kotlin 版本 - 编译时保证空安全

```kotlin
class UserServiceKotlin {
    fun getUserName(user: PersonKotlin?): String {
        return user?.name?.uppercase() ?: "Unknown"
    }

    fun getNameLength(user: PersonKotlin?): Int {
        return user?.name?.length ?: 0
    }
}

fun nullSafetyComparisonDemo() {
    println("\n=== 空安全对比 ===")

    val service = UserServiceKotlin()
    val user: PersonKotlin? = null
    val validUser = PersonKotlin("Alice", 25, "<EMAIL>")

    println("Null user name: ${service.getUserName(user)}")
    println("Valid user name: ${service.getUserName(validUser)}")
    println("Null user name length: ${service.getNameLength(user)}")
    println("Valid user name length: ${service.getNameLength(validUser)}")

    // Kotlin 的空安全让代码更简洁、更安全
}
```

## 3. 函数式编程对比

### Java 8+ 版本

```java
public class CollectionProcessorJava {
    public List<String> processNames(List<Person> people) {
        return people.stream()
                .filter(person -> person.getAge() >= 18)
                .map(person -> person.getName().toUpperCase())
                .collect(Collectors.toList());
    }

    public Optional<Person> findOldest(List<Person> people) {
        return people.stream()
                .max(Comparator.comparing(Person::getAge));
    }
}
```

### Kotlin 版本 - 更简洁的函数式编程

```kotlin
class CollectionProcessorKotlin {
    fun processNames(people: List<PersonKotlin>): List<String> {
        return people
            .filter { it.age >= 18 }
            .map { it.name.uppercase() }
    }

    fun findOldest(people: List<PersonKotlin>): PersonKotlin? {
        return people.maxByOrNull { it.age }
    }
}

fun functionalProgrammingDemo() {
    println("\n=== 函数式编程对比 ===")

    val people = listOf(
        PersonKotlin("Alice", 25, "<EMAIL>"),
        PersonKotlin("Bob", 17, "<EMAIL>"),
        PersonKotlin("Charlie", 30, "<EMAIL>"),
        PersonKotlin("Diana", 22, "<EMAIL>")
    )

    val processor = CollectionProcessorKotlin()

    val adultNames = processor.processNames(people)
    println("Adult names: $adultNames")

    val oldest = processor.findOldest(people)
    println("Oldest person: $oldest")

    // Kotlin 的集合操作更直观、更简洁
}
```

## 4. 字符串处理对比

### Java 版本

```java
public class StringUtilsJava {
    public String formatUserInfo(String name, int age, String city) {
        return "User: " + name + ", Age: " + age + ", City: " + city;
    }

    public String createMultilineText(String title, String content) {
        StringBuilder sb = new StringBuilder();
        sb.append("Title: ").append(title).append("\n");
        sb.append("Content:\n");
        sb.append(content);
        return sb.toString();
    }
}
```

### Kotlin 版本

```kotlin
class StringUtilsKotlin {
    fun formatUserInfo(name: String, age: Int, city: String): String {
        return "User: $name, Age: $age, City: $city"
    }

    fun createMultilineText(title: String, content: String): String {
        return """
            |Title: $title
            |Content:
            |$content
        """.trimMargin()
    }
}

fun stringProcessingDemo() {
    println("\n=== 字符串处理对比 ===")

    val utils = StringUtilsKotlin()

    val userInfo = utils.formatUserInfo("Alice", 25, "Beijing")
    println(userInfo)

    val multilineText = utils.createMultilineText(
        "Kotlin vs Java",
        "Kotlin provides more concise syntax for string manipulation"
    )
    println(multilineText)

    // Kotlin 的字符串模板和多行字符串更方便
}
```

## 5. 扩展函数对比

### Java 版本 - 需要工具类

```java
public class StringUtilsJava {
    public static boolean isPalindrome(String str) {
        return str.equals(new StringBuilder(str).reverse().toString());
    }

    public static String truncate(String str, int maxLength) {
        if (str.length() <= maxLength) {
            return str;
        }
        return str.substring(0, maxLength) + "...";
    }
}

// 使用：StringUtilsJava.isPalindrome("level")
```

### Kotlin 版本 - 扩展函数

```kotlin
fun String.isPalindrome(): Boolean {
    return this == this.reversed()
}

fun String.truncate(maxLength: Int): String {
    return if (length <= maxLength) this else take(maxLength) + "..."
}

fun extensionFunctionDemo() {
    println("\n=== 扩展函数对比 ===")

    val text = "level"
    val longText = "This is a very long text that needs to be truncated"

    // Kotlin 扩展函数让代码更自然
    println("'$text' is palindrome: ${text.isPalindrome()}")
    println("Truncated: ${longText.truncate(20)}")

    // Java 需要静态方法调用，Kotlin 可以直接在对象上调用
}
```

## 6. 异步编程对比

### Java 版本 - 使用 CompletableFuture

```java
public class AsyncServiceJava {
    private ExecutorService executor = Executors.newFixedThreadPool(4);

    public CompletableFuture<String> fetchUserData(String userId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                Thread.sleep(1000); // 模拟网络请求
                return "User data for " + userId;
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }, executor);
    }

    public CompletableFuture<String> processUserData(String userId) {
        return fetchUserData(userId)
                .thenCompose(userData -> {
                    return CompletableFuture.supplyAsync(() -> {
                        try {
                            Thread.sleep(500); // 模拟处理
                            return "Processed: " + userData;
                        } catch (InterruptedException e) {
                            throw new RuntimeException(e);
                        }
                    }, executor);
                });
    }
}
```

### Kotlin 版本 - 使用协程

```kotlin
import kotlinx.coroutines.*

class AsyncServiceKotlin {
    suspend fun fetchUserData(userId: String): String {
        delay(1000) // 模拟网络请求
        return "User data for $userId"
    }

    suspend fun processUserData(userId: String): String {
        val userData = fetchUserData(userId)
        delay(500) // 模拟处理
        return "Processed: $userData"
    }
}

fun asyncProgrammingDemo() {
    println("\n=== 异步编程对比 ===")

    val service = AsyncServiceKotlin()

    runBlocking {
        val startTime = System.currentTimeMillis()

        // 顺序执行
        val result1 = service.processUserData("123")
        println("Result 1: $result1")

        // 并发执行
        val deferred1 = async { service.processUserData("456") }
        val deferred2 = async { service.processUserData("789") }

        val result2 = deferred1.await()
        val result3 = deferred2.await()

        println("Result 2: $result2")
        println("Result 3: $result3")

        val endTime = System.currentTimeMillis()
        println("Total time: ${endTime - startTime}ms")
    }

    // Kotlin 协程比 Java CompletableFuture 更简洁、更易读
}
```

## 7. 单例模式对比

### Java 版本 - 复杂的单例实现

```java
public class DatabaseManagerJava {
    private static volatile DatabaseManagerJava instance;
    private String connectionString;

    private DatabaseManagerJava() {
        connectionString = "******************************";
    }

    public static DatabaseManagerJava getInstance() {
        if (instance == null) {
            synchronized (DatabaseManagerJava.class) {
                if (instance == null) {
                    instance = new DatabaseManagerJava();
                }
            }
        }
        return instance;
    }

    public void connect() {
        System.out.println("Connecting to: " + connectionString);
    }
}
```

### Kotlin 版本 - 简单的 object 声明

```kotlin
object DatabaseManagerKotlin {
    private const val connectionString = "******************************"

    fun connect() {
        println("Connecting to: $connectionString")
    }
}

fun singletonDemo() {
    println("\n=== 单例模式对比 ===")

    // Kotlin object 自动是线程安全的单例
    DatabaseManagerKotlin.connect()

    // Java 需要复杂的双重检查锁定，Kotlin 只需要 object 关键字
}
```

## 8. 代码行数统计对比

```kotlin
fun codeLineComparison() {
    println("\n=== 代码行数对比 ===")

    println("功能实现代码行数对比：")
    println("1. 数据类定义:")
    println("   Java: ~50 行")
    println("   Kotlin: 1 行")
    println()

    println("2. 空安全处理:")
    println("   Java: ~15 行")
    println("   Kotlin: 1 行")
    println()

    println("3. 集合操作:")
    println("   Java: ~8 行")
    println("   Kotlin: 3 行")
    println()

    println("4. 字符串模板:")
    println("   Java: ~5 行")
    println("   Kotlin: 1 行")
    println()

    println("5. 单例模式:")
    println("   Java: ~20 行")
    println("   Kotlin: 5 行")
    println()

    println("总体而言，Kotlin 可以减少 40-60% 的样板代码！")
}
```

## 9. 主函数和运行示例

    asyncProgrammingDemo()
    singletonDemo()
    codeLineComparison()

    println("\n" + "=" * 50)
    println("对比演示完成！")
    println("结论：Kotlin 在简洁性、安全性、表达力方面都优于 Java")
}

// 扩展操作符
operator fun String.times(n: Int): String = this.repeat(n)
```

## 运行结果示例

当运行这个对比程序时，你会看到类似以下的输出：

```
Kotlin vs Java 对比示例
==================================================
=== 类定义对比 ===
Person1: PersonKotlin(name=Alice, age=25, email=<EMAIL>)
Person2: PersonKotlin(name=Bob, age=25, email=<EMAIL>)
Are equal: false

=== 空安全对比 ===
Null user name: Unknown
Valid user name: ALICE
Null user name length: 0
Valid user name length: 5

=== 函数式编程对比 ===
Adult names: [ALICE, CHARLIE, DIANA]
Oldest person: PersonKotlin(name=Charlie, age=30, email=<EMAIL>)

=== 字符串处理对比 ===
User: Alice, Age: 25, City: Beijing
Title: Kotlin vs Java
Content:
Kotlin provides more concise syntax for string manipulation

=== 扩展函数对比 ===
'level' is palindrome: true
Truncated: This is a very long...

=== 异步编程对比 ===
Result 1: Processed: User data for 123
Result 2: Processed: User data for 456
Result 3: Processed: User data for 789
Total time: 3000ms

=== 单例模式对比 ===
Connecting to: ******************************

=== 代码行数对比 ===
功能实现代码行数对比：
1. 数据类定义:
   Java: ~50 行
   Kotlin: 1 行

2. 空安全处理:
   Java: ~15 行
   Kotlin: 1 行

3. 集合操作:
   Java: ~8 行
   Kotlin: 3 行

4. 字符串模板:
   Java: ~5 行
   Kotlin: 1 行

5. 单例模式:
   Java: ~20 行
   Kotlin: 5 行

总体而言，Kotlin 可以减少 40-60% 的样板代码！

==================================================
对比演示完成！
结论：Kotlin 在简洁性、安全性、表达力方面都优于 Java
```

## 总结

这个对比示例清楚地展示了 Kotlin 相对于 Java 的主要优势：

1. **简洁性**：大幅减少样板代码
2. **安全性**：编译时空安全保证
3. **表达力**：更直观的语法和特性
4. **现代性**：原生支持函数式编程和协程
5. **互操作性**：与 Java 100% 兼容

Kotlin 不仅让代码更简洁，还让开发更高效、更安全！
