# Kotlin 基础语法示例

展示 Kotlin 的核心特性和语法

## 1. 变量声明

```kotlin
fun variableDemo() {
    println("=== 变量声明示例 ===")

    // 可变变量
    var name: String = "Kotlin"
    var age = 25  // 类型推断

    // 不可变变量（推荐）
    val language: String = "Kotlin"
    val version = "1.9"

    // 延迟初始化
    lateinit var database: String
    database = "MySQL"

    println("Name: $name, Age: $age")
    println("Language: $language, Version: $version")
    println("Database: $database")
}
```

## 2. 空安全示例

```kotlin
fun nullSafetyDemo() {
    println("\n=== 空安全示例 ===")

    var nullableName: String? = null
    var nonNullName: String = "Kotlin"

    // 安全调用
    val length = nullableName?.length
    println("Nullable name length: $length")

    // Elvis 操作符
    val nameLength = nullableName?.length ?: 0
    println("Name length with default: $nameLength")

    // 安全转换
    nullableName = "Hello"
    val definiteLength = nullableName?.length ?: 0
    println("Definite length: $definiteLength")
}
```

## 3. 函数示例

```kotlin
fun functionDemo() {
    println("\n=== 函数示例 ===")

    // 基本函数
    fun greet(name: String): String {
        return "Hello, $name!"
    }

    // 单表达式函数
    fun add(a: Int, b: Int) = a + b

    // 默认参数
    fun createUser(name: String, age: Int = 18, city: String = "Beijing") {
        println("User: $name, Age: $age, City: $city")
    }

    println(greet("World"))
    println("Add result: ${add(5, 3)}")

    // 命名参数
    createUser(name = "Alice")
    createUser(name = "Bob", city = "Shanghai")
    createUser("Charlie", 25, "Guangzhou")
}
```

## 4. 字符串模板示例

```kotlin
fun stringTemplateDemo() {
    println("\n=== 字符串模板示例 ===")

    val name = "Kotlin"
    val version = 1.9

    // 简单插值
    val message = "Welcome to $name"
    println(message)

    // 表达式插值
    val info = "Current version is ${version.toString()}"
    println(info)

    // 多行字符串
    val multiLine = """
        |This is a
        |multi-line
        |string in $name
    """.trimMargin()
    println(multiLine)
}
```

## 5. 类和对象示例

```kotlin
// 基本类定义
class Person(val name: String, var age: Int) {
    // 次构造函数
    constructor(name: String) : this(name, 0)

    // 方法
    fun introduce() {
        println("Hi, I'm $name, $age years old")
    }

    // 属性访问器
    val isAdult: Boolean
        get() = age >= 18
}

// 数据类
data class User(val id: Int, val name: String, val email: String)

// 密封类
sealed class Result {
    data class Success(val data: String) : Result()
    data class Error(val exception: Throwable) : Result()
    object Loading : Result()
}

fun classDemo() {
    println("\n=== 类和对象示例 ===")

    // 基本类使用
    val person1 = Person("Alice", 25)
    val person2 = Person("Bob")

    person1.introduce()
    person2.introduce()
    println("Alice is adult: ${person1.isAdult}")
    println("Bob is adult: ${person2.isAdult}")

    // 数据类使用
    val user1 = User(1, "Alice", "<EMAIL>")
    val user2 = user1.copy(name = "Bob")

    println("User1: $user1")
    println("User2: $user2")
    println("Users equal: ${user1 == user2}")

    // 密封类使用
    fun handleResult(result: Result) {
        when (result) {
            is Result.Success -> println("Success: ${result.data}")
            is Result.Error -> println("Error: ${result.exception.message}")
            Result.Loading -> println("Loading...")
        }
    }

    handleResult(Result.Success("Data loaded"))
    handleResult(Result.Error(RuntimeException("Network error")))
    handleResult(Result.Loading)
}
```

## 6. 集合操作示例

```kotlin
fun collectionDemo() {
    println("\n=== 集合操作示例 ===")

    val numbers = listOf(1, 2, 3, 4, 5, 6, 7, 8, 9, 10)

    // 过滤
    val evenNumbers = numbers.filter { it % 2 == 0 }
    println("Even numbers: $evenNumbers")

    // 映射
    val doubled = numbers.map { it * 2 }
    println("Doubled: $doubled")

    // 查找
    val firstEven = numbers.find { it % 2 == 0 }
    println("First even: $firstEven")

    // 分组
    val grouped = numbers.groupBy { it % 3 }
    println("Grouped by mod 3: $grouped")

    // 聚合
    val sum = numbers.sum()
    val max = numbers.maxOrNull()
    println("Sum: $sum, Max: $max")

    // 链式操作
    val result = numbers
        .filter { it % 2 == 0 }
        .map { it * it }
        .take(3)
    println("Chain result: $result")
}
```

## 7. 扩展函数示例

```kotlin
// 扩展函数定义
fun String.isPalindrome(): Boolean {
    return this == this.reversed()
}

fun <T> List<T>.secondOrNull(): T? {
    return if (size >= 2) this[1] else null
}

fun extensionDemo() {
    println("\n=== 扩展函数示例 ===")

    val text1 = "level"
    val text2 = "hello"

    println("'$text1' is palindrome: ${text1.isPalindrome()}")
    println("'$text2' is palindrome: ${text2.isPalindrome()}")

    val list = listOf("first", "second", "third")
    val emptyList = emptyList<String>()

    println("Second element: ${list.secondOrNull()}")
    println("Second element of empty list: ${emptyList.secondOrNull()}")
}
```

## 8. 高阶函数示例

```kotlin
fun higherOrderFunctionDemo() {
    println("\n=== 高阶函数示例 ===")

    // 高阶函数定义
    fun processNumbers(numbers: List<Int>, operation: (Int) -> Int): List<Int> {
        return numbers.map(operation)
    }

    // 函数类型参数
    fun calculate(x: Int, y: Int, operation: (Int, Int) -> Int): Int {
        return operation(x, y)
    }

    val numbers = listOf(1, 2, 3, 4, 5)

    // 使用 Lambda
    val doubled = processNumbers(numbers) { it * 2 }
    val squared = processNumbers(numbers) { it * it }

    println("Original: $numbers")
    println("Doubled: $doubled")
    println("Squared: $squared")

    // 函数作为参数
    val addResult = calculate(5, 3) { a, b -> a + b }
    val multiplyResult = calculate(5, 3) { a, b -> a * b }

    println("5 + 3 = $addResult")
    println("5 * 3 = $multiplyResult")
}
```

## 9. 作用域函数示例

```kotlin
fun scopeFunctionDemo() {
    println("\n=== 作用域函数示例 ===")

    // let - 对象配置和空值处理
    val result = "Hello".let {
        println("Processing: $it")
        it.length
    }
    println("Length: $result")

    // apply - 对象配置
    val person = Person("Alice", 25).apply {
        age = 26
        println("Configured person: $name")
    }
    person.introduce()

    // run - 对象配置和计算结果
    val length = "Hello World".run {
        println("Processing: $this")
        length
    }
    println("String length: $length")

    // also - 附加操作
    val numbers = mutableListOf(1, 2, 3).also {
        println("Created list: $it")
        it.add(4)
    }
    println("Final list: $numbers")

    // with - 多个操作
    val stringResult = with(StringBuilder()) {
        append("Hello")
        append(" ")
        append("World")
        toString()
    }
    println("Built string: $stringResult")
}
```

## 10. 主函数和扩展操作符

```kotlin
// 主函数 - 运行所有示例
fun main() {
    println("Kotlin 基础语法示例演示")
    println("=" * 50)

    variableDemo()
    nullSafetyDemo()
    functionDemo()
    stringTemplateDemo()
    classDemo()
    collectionDemo()
    extensionDemo()
    higherOrderFunctionDemo()
    scopeFunctionDemo()

    println("\n" + "=" * 50)
    println("所有示例演示完成！")
}

// 扩展操作符（用于字符串重复）
operator fun String.times(n: Int): String {
    return this.repeat(n)
}
```

## 运行结果示例

当运行这个程序时，你会看到类似以下的输出：

```
Kotlin 基础语法示例演示
==================================================
=== 变量声明示例 ===
Name: Kotlin, Age: 25
Language: Kotlin, Version: 1.9
Database: MySQL

=== 空安全示例 ===
Nullable name length: null
Name length with default: 0
Definite length: 5

=== 函数示例 ===
Hello, World!
Add result: 8
User: Alice, Age: 18, City: Beijing
User: Bob, Age: 18, City: Shanghai
User: Charlie, Age: 25, City: Guangzhou

... (更多输出)
==================================================
所有示例演示完成！
```
