/**
 * Kotlin 协程使用示例
 * 展示协程的各种用法和最佳实践
 */

import kotlinx.coroutines.*
import kotlin.random.Random
import kotlin.system.measureTimeMillis

// 模拟数据类
data class User(val id: String, val name: String, val email: String)
data class Post(val id: String, val title: String, val content: String)
data class Comment(val id: String, val content: String, val author: String)

// 1. 基础协程示例
fun basicCoroutineDemo() {
    println("=== 基础协程示例 ===")
    
    // runBlocking - 阻塞当前线程
    runBlocking {
        println("Hello from runBlocking")
        delay(1000)
        println("World!")
    }
    
    // launch - 启动新协程（不阻塞）
    runBlocking {
        val job = launch {
            delay(1000)
            println("Hello from launch")
        }
        
        println("Main thread continues...")
        job.join() // 等待协程完成
    }
    
    // async - 启动新协程并返回结果
    runBlocking {
        val deferred = async {
            delay(1000)
            "Hello from async"
        }
        
        println("Waiting for result...")
        val result = deferred.await()
        println(result)
    }
}

// 2. 挂起函数示例
suspend fun fetchUser(userId: String): User {
    println("Fetching user $userId...")
    delay(500) // 模拟网络请求
    return User(userId, "User $userId", "$<EMAIL>")
}

suspend fun fetchUserPosts(userId: String): List<Post> {
    println("Fetching posts for user $userId...")
    delay(800) // 模拟网络请求
    return listOf(
        Post("1", "Post 1", "Content 1"),
        Post("2", "Post 2", "Content 2")
    )
}

suspend fun fetchPostComments(postId: String): List<Comment> {
    println("Fetching comments for post $postId...")
    delay(300) // 模拟网络请求
    return listOf(
        Comment("1", "Great post!", "Alice"),
        Comment("2", "Thanks for sharing", "Bob")
    )
}

fun suspendFunctionDemo() {
    println("\n=== 挂起函数示例 ===")
    
    runBlocking {
        // 顺序执行
        val user = fetchUser("123")
        println("User: $user")
        
        val posts = fetchUserPosts(user.id)
        println("Posts: ${posts.size} posts found")
        
        val comments = fetchPostComments(posts[0].id)
        println("Comments: ${comments.size} comments found")
    }
}

// 3. 并发执行示例
fun concurrentDemo() {
    println("\n=== 并发执行示例 ===")
    
    runBlocking {
        // 顺序执行 - 慢
        val sequentialTime = measureTimeMillis {
            val user = fetchUser("123")
            val posts = fetchUserPosts("123")
            println("Sequential: User and posts loaded")
        }
        println("Sequential time: ${sequentialTime}ms")
        
        // 并发执行 - 快
        val concurrentTime = measureTimeMillis {
            val userDeferred = async { fetchUser("123") }
            val postsDeferred = async { fetchUserPosts("123") }
            
            val user = userDeferred.await()
            val posts = postsDeferred.await()
            println("Concurrent: User and posts loaded")
        }
        println("Concurrent time: ${concurrentTime}ms")
    }
}

// 4. 协程作用域示例
class UserRepository {
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    fun loadUserData(userId: String, callback: (User) -> Unit) {
        scope.launch {
            try {
                val user = fetchUser(userId)
                withContext(Dispatchers.Main) {
                    callback(user)
                }
            } catch (e: Exception) {
                println("Error loading user: ${e.message}")
            }
        }
    }
    
    fun cleanup() {
        scope.cancel()
    }
}

fun coroutineScopeDemo() {
    println("\n=== 协程作用域示例 ===")
    
    val repository = UserRepository()
    
    runBlocking {
        repository.loadUserData("123") { user ->
            println("Loaded user: $user")
        }
        
        delay(1000) // 等待异步操作完成
        repository.cleanup()
    }
}

// 5. 调度器示例
fun dispatcherDemo() {
    println("\n=== 调度器示例 ===")
    
    runBlocking {
        // Main 调度器（在 Android 中是主线程）
        launch(Dispatchers.Main) {
            println("Main dispatcher: ${Thread.currentThread().name}")
        }
        
        // IO 调度器 - 适合 I/O 操作
        launch(Dispatchers.IO) {
            println("IO dispatcher: ${Thread.currentThread().name}")
            // 模拟文件读取
            delay(100)
        }
        
        // Default 调度器 - 适合 CPU 密集型任务
        launch(Dispatchers.Default) {
            println("Default dispatcher: ${Thread.currentThread().name}")
            // 模拟复杂计算
            repeat(1000) { /* 计算 */ }
        }
        
        // 上下文切换
        withContext(Dispatchers.IO) {
            println("Switched to IO: ${Thread.currentThread().name}")
            val data = "data from IO"
            
            withContext(Dispatchers.Main) {
                println("Switched to Main: ${Thread.currentThread().name}")
                println("Processing: $data")
            }
        }
    }
}

// 6. 异常处理示例
suspend fun riskyOperation(): String {
    delay(100)
    if (Random.nextBoolean()) {
        throw RuntimeException("Random failure")
    }
    return "Success"
}

fun exceptionHandlingDemo() {
    println("\n=== 异常处理示例 ===")
    
    runBlocking {
        // 1. try-catch
        try {
            val result = riskyOperation()
            println("Result: $result")
        } catch (e: Exception) {
            println("Caught exception: ${e.message}")
        }
        
        // 2. CoroutineExceptionHandler
        val handler = CoroutineExceptionHandler { _, exception ->
            println("Caught by handler: $exception")
        }
        
        val job = launch(handler) {
            throw RuntimeException("Test exception")
        }
        job.join()
        
        // 3. SupervisorJob - 子协程异常不影响其他子协程
        val supervisor = SupervisorJob()
        val scope = CoroutineScope(Dispatchers.Default + supervisor)
        
        scope.launch {
            delay(100)
            throw RuntimeException("Child 1 failed")
        }
        
        scope.launch {
            delay(200)
            println("Child 2 completed successfully")
        }
        
        delay(300)
        scope.cancel()
    }
}

// 7. 协程取消示例
fun cancellationDemo() {
    println("\n=== 协程取消示例 ===")
    
    runBlocking {
        // 基本取消
        val job = launch {
            repeat(1000) { i ->
                if (!isActive) {
                    println("Cancelled at iteration $i")
                    return@launch
                }
                println("Working $i")
                delay(100)
            }
        }
        
        delay(500)
        println("Cancelling job...")
        job.cancel()
        job.join()
        
        // 不可取消的操作
        val nonCancellableJob = launch {
            try {
                repeat(1000) { i ->
                    println("Non-cancellable work $i")
                    Thread.sleep(100) // 这不会响应取消
                }
            } finally {
                println("Cleanup in finally")
            }
        }
        
        delay(300)
        nonCancellableJob.cancel()
        
        // 正确的可取消操作
        val cancellableJob = launch {
            try {
                repeat(1000) { i ->
                    ensureActive() // 检查取消状态
                    println("Cancellable work $i")
                    delay(100) // delay 会自动检查取消
                }
            } finally {
                withContext(NonCancellable) {
                    println("Cleanup with NonCancellable")
                    delay(100) // 清理工作不会被取消
                }
            }
        }
        
        delay(300)
        cancellableJob.cancel()
        cancellableJob.join()
    }
}

// 8. 实际应用示例 - 模拟 Android ViewModel
class UserViewModel {
    private val viewModelScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    private val repository = UserRepository()
    
    fun loadUserProfile(userId: String) {
        viewModelScope.launch {
            try {
                // 显示加载状态
                println("Loading user profile...")
                
                // 并发加载用户数据
                val userDeferred = async(Dispatchers.IO) { fetchUser(userId) }
                val postsDeferred = async(Dispatchers.IO) { fetchUserPosts(userId) }
                
                val user = userDeferred.await()
                val posts = postsDeferred.await()
                
                // 在主线程更新 UI
                withContext(Dispatchers.Main) {
                    println("Profile loaded: ${user.name} with ${posts.size} posts")
                }
                
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    println("Error loading profile: ${e.message}")
                }
            }
        }
    }
    
    fun onCleared() {
        viewModelScope.cancel()
        repository.cleanup()
    }
}

fun realWorldDemo() {
    println("\n=== 实际应用示例 ===")
    
    val viewModel = UserViewModel()
    
    runBlocking {
        viewModel.loadUserProfile("123")
        delay(2000) // 等待加载完成
        viewModel.onCleared()
    }
}

// 9. 协程性能对比示例
fun performanceDemo() {
    println("\n=== 性能对比示例 ===")
    
    // 协程创建性能测试
    val coroutineTime = measureTimeMillis {
        runBlocking {
            repeat(10000) {
                launch {
                    delay(1)
                }
            }
        }
    }
    println("10000 协程创建时间: ${coroutineTime}ms")
    
    // 线程创建性能测试（注意：这会创建很多线程，可能影响系统）
    val threadTime = measureTimeMillis {
        val threads = mutableListOf<Thread>()
        repeat(1000) { // 减少数量避免系统问题
            val thread = Thread {
                Thread.sleep(1)
            }
            threads.add(thread)
            thread.start()
        }
        threads.forEach { it.join() }
    }
    println("1000 线程创建时间: ${threadTime}ms")
}

// 主函数 - 运行所有示例
fun main() {
    println("Kotlin 协程示例演示")
    println("=" * 50)
    
    basicCoroutineDemo()
    suspendFunctionDemo()
    concurrentDemo()
    coroutineScopeDemo()
    dispatcherDemo()
    exceptionHandlingDemo()
    cancellationDemo()
    realWorldDemo()
    performanceDemo()
    
    println("\n" + "=" * 50)
    println("所有协程示例演示完成！")
}

// 扩展操作符（用于字符串重复）
operator fun String.times(n: Int): String {
    return this.repeat(n)
}
