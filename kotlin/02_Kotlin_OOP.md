# Kotlin 面向对象编程

## 1. 类和对象基础

### 1.1 类的定义

```kotlin
// 基本类定义
class Person {
    var name: String = ""
    var age: Int = 0
}

// 主构造函数
class Person(val name: String, var age: Int) {
    // 初始化块
    init {
        println("Person created: $name, age $age")
    }
}

// 次构造函数
class Person(val name: String, var age: Int) {
    constructor(name: String) : this(name, 0) {
        println("Secondary constructor called")
    }
}
```

### 1.2 属性和访问器

```kotlin
class Rectangle(val width: Double, val height: Double) {
    // 计算属性
    val area: Double
        get() = width * height
    
    // 自定义访问器
    var isSquare: Boolean = false
        get() = width == height
        set(value) {
            field = value
            if (value && width != height) {
                println("Warning: Rectangle is not actually square")
            }
        }
    
    // 延迟初始化属性
    lateinit var description: String
    
    // 延迟计算属性
    val perimeter: Double by lazy {
        println("Computing perimeter...")
        2 * (width + height)
    }
}
```

## 2. 继承

### 2.1 基本继承

```kotlin
// 基类必须用 open 关键字
open class Animal(val name: String) {
    open fun makeSound() {
        println("$name makes a sound")
    }
    
    // final 方法不能被重写
    final fun sleep() {
        println("$name is sleeping")
    }
}

// 继承
class Dog(name: String, val breed: String) : Animal(name) {
    override fun makeSound() {
        println("$name barks")
    }
    
    fun wagTail() {
        println("$name wags tail")
    }
}
```

### 2.2 抽象类

```kotlin
abstract class Shape {
    abstract val area: Double
    abstract fun draw()
    
    // 抽象类可以有具体实现
    fun printInfo() {
        println("Shape area: $area")
    }
}

class Circle(private val radius: Double) : Shape() {
    override val area: Double
        get() = Math.PI * radius * radius
    
    override fun draw() {
        println("Drawing a circle with radius $radius")
    }
}
```

## 3. 接口

### 3.1 接口定义和实现

```kotlin
interface Drawable {
    // 抽象方法
    fun draw()
    
    // 默认实现
    fun getInfo(): String = "This is a drawable object"
    
    // 属性（必须是抽象的或提供访问器）
    val color: String
}

interface Clickable {
    fun click()
    fun showTooltip() = println("Tooltip shown")
}

// 实现多个接口
class Button : Drawable, Clickable {
    override val color: String = "Blue"
    
    override fun draw() {
        println("Drawing button")
    }
    
    override fun click() {
        println("Button clicked")
    }
}
```

### 3.2 接口冲突解决

```kotlin
interface A {
    fun foo() = println("A.foo()")
}

interface B {
    fun foo() = println("B.foo()")
}

class C : A, B {
    override fun foo() {
        super<A>.foo() // 调用 A 的实现
        super<B>.foo() // 调用 B 的实现
    }
}
```

## 4. 数据类

### 4.1 数据类基础

```kotlin
data class User(val id: Int, val name: String, val email: String)

fun dataClassDemo() {
    val user1 = User(1, "Alice", "<EMAIL>")
    
    // 自动生成的方法
    println(user1) // toString()
    
    val user2 = User(1, "Alice", "<EMAIL>")
    println(user1 == user2) // equals() - true
    println(user1.hashCode() == user2.hashCode()) // hashCode()
    
    // copy 方法
    val user3 = user1.copy(name = "Bob")
    println(user3)
    
    // 解构声明
    val (id, name, email) = user1
    println("ID: $id, Name: $name, Email: $email")
}
```

### 4.2 数据类限制

```kotlin
// 数据类要求：
// 1. 主构造函数至少有一个参数
// 2. 主构造函数的参数必须标记为 val 或 var
// 3. 不能是 abstract、open、sealed 或 inner

data class Point(val x: Int, val y: Int) {
    // 可以有类体
    fun distanceFromOrigin(): Double {
        return Math.sqrt((x * x + y * y).toDouble())
    }
}
```

## 5. 密封类

### 5.1 密封类定义

```kotlin
sealed class Result<out T> {
    data class Success<T>(val data: T) : Result<T>()
    data class Error(val exception: Throwable) : Result<Nothing>()
    object Loading : Result<Nothing>()
}

// 使用密封类
fun handleResult(result: Result<String>) {
    when (result) {
        is Result.Success -> println("Data: ${result.data}")
        is Result.Error -> println("Error: ${result.exception.message}")
        Result.Loading -> println("Loading...")
        // 不需要 else 分支，编译器保证完整性
    }
}
```

### 5.2 密封类的优势

```kotlin
// 表示状态机
sealed class NetworkState {
    object Idle : NetworkState()
    object Loading : NetworkState()
    data class Success(val data: String) : NetworkState()
    data class Error(val message: String) : NetworkState()
}

class NetworkManager {
    private var state: NetworkState = NetworkState.Idle
    
    fun handleState() {
        when (state) {
            NetworkState.Idle -> startLoading()
            NetworkState.Loading -> showProgress()
            is NetworkState.Success -> displayData(state.data)
            is NetworkState.Error -> showError(state.message)
        }
    }
    
    private fun startLoading() { /* ... */ }
    private fun showProgress() { /* ... */ }
    private fun displayData(data: String) { /* ... */ }
    private fun showError(message: String) { /* ... */ }
}
```

## 6. 枚举类

### 6.1 基本枚举

```kotlin
enum class Direction {
    NORTH, SOUTH, WEST, EAST
}

enum class Color(val rgb: Int) {
    RED(0xFF0000),
    GREEN(0x00FF00),
    BLUE(0x0000FF)
}
```

### 6.2 枚举方法和属性

```kotlin
enum class Planet(val mass: Double, val radius: Double) {
    MERCURY(3.303e+23, 2.4397e6),
    VENUS(4.869e+24, 6.0518e6),
    EARTH(5.976e+24, 6.37814e6);
    
    private val G = 6.67300E-11
    
    val surfaceGravity: Double
        get() = G * mass / (radius * radius)
    
    fun surfaceWeight(otherMass: Double): Double {
        return otherMass * surfaceGravity
    }
}
```

## 7. 对象声明和表达式

### 7.1 单例对象

```kotlin
object DatabaseManager {
    private const val URL = "******************************"
    
    fun connect() {
        println("Connecting to $URL")
    }
    
    fun disconnect() {
        println("Disconnecting from database")
    }
}

// 使用
DatabaseManager.connect()
```

### 7.2 对象表达式

```kotlin
interface EventListener {
    fun onEvent(event: String)
}

fun createListener(): EventListener {
    return object : EventListener {
        override fun onEvent(event: String) {
            println("Event received: $event")
        }
    }
}

// 匿名对象访问外部变量
fun createCounter(): () -> Int {
    var count = 0
    return object {
        fun increment(): Int = ++count
    }::increment
}
```

### 7.3 伴生对象

```kotlin
class MyClass {
    companion object Factory {
        const val CONSTANT = "constant_value"
        
        fun create(): MyClass = MyClass()
        
        @JvmStatic
        fun staticMethod() {
            println("This is a static method")
        }
    }
}

// 使用
val instance = MyClass.create()
println(MyClass.CONSTANT)
```

## 8. 内部类和嵌套类

### 8.1 嵌套类

```kotlin
class Outer {
    private val outerProperty = "Outer property"
    
    // 嵌套类（不能访问外部类实例）
    class Nested {
        fun doSomething() {
            // println(outerProperty) // 编译错误
            println("Nested class method")
        }
    }
}

// 使用
val nested = Outer.Nested()
```

### 8.2 内部类

```kotlin
class Outer {
    private val outerProperty = "Outer property"
    
    // 内部类（可以访问外部类实例）
    inner class Inner {
        fun doSomething() {
            println(outerProperty) // 可以访问
            println("Inner class method")
        }
        
        fun getOuter(): Outer = this@Outer
    }
}

// 使用
val outer = Outer()
val inner = outer.Inner()
```

## 9. 委托

### 9.1 类委托

```kotlin
interface Base {
    fun print()
}

class BaseImpl(val x: Int) : Base {
    override fun print() { println(x) }
}

// 委托给 BaseImpl
class Derived(b: Base) : Base by b

fun delegationDemo() {
    val base = BaseImpl(10)
    val derived = Derived(base)
    derived.print() // 输出: 10
}
```

### 9.2 属性委托

```kotlin
import kotlin.properties.Delegates

class User {
    // 延迟初始化
    val name: String by lazy {
        println("Computing name...")
        "John Doe"
    }
    
    // 可观察属性
    var age: Int by Delegates.observable(0) { prop, old, new ->
        println("Age changed from $old to $new")
    }
    
    // 非空属性
    var email: String by Delegates.notNull()
}
```

## 10. 面试重点

### 常见面试问题：

1. **Kotlin 中 open 关键字的作用？**
   - 类和方法默认是 final 的，需要 open 才能被继承/重写

2. **数据类有什么限制？**
   - 主构造函数至少一个参数，参数必须是 val/var，不能是 abstract/open/sealed/inner

3. **密封类的使用场景？**
   - 表示受限的类层次结构，常用于状态管理和结果封装

4. **object 和 class 的区别？**
   - object 是单例，class 可以创建多个实例

5. **内部类和嵌套类的区别？**
   - 内部类可以访问外部类实例，嵌套类不能

### 最佳实践：
- 优先使用数据类而不是普通类
- 合理使用密封类表示状态
- 利用委托减少样板代码
- 正确使用 open 关键字控制继承
- 善用伴生对象替代静态方法
