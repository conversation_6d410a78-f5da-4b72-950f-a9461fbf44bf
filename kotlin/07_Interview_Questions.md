# Kotlin 面试常见问题

## 1. 基础语法类

### Q1: Kotlin 相比 Java 有哪些优势？

**答案要点**：
- **空安全**：编译时防止 NPE
- **简洁性**：减少样板代码
- **互操作性**：与 Java 100% 兼容
- **函数式编程**：支持高阶函数、Lambda
- **协程**：原生异步编程支持
- **扩展函数**：为现有类添加功能

**示例对比**：
```kotlin
// Java
public class Person {
    private String name;
    public Person(String name) { this.name = name; }
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
}

// Kotlin
data class Person(var name: String)
```

### Q2: val 和 var 的区别？

**答案**：
- `val`：只读变量，类似 Java 的 final
- `var`：可变变量

**深入理解**：
```kotlin
val list = mutableListOf(1, 2, 3)
list.add(4) // ✅ 可以修改内容
// list = mutableListOf(5, 6) // ❌ 不能重新赋值

var number = 10
number = 20 // ✅ 可以重新赋值
```

### Q3: 什么是空安全？如何实现的？

**答案**：
- **概念**：编译时防止空指针异常
- **实现**：类型系统区分可空(?)和非空类型

```kotlin
var name: String = "Kotlin"     // 非空类型
var nullableName: String? = null // 可空类型

// 安全调用
val length = nullableName?.length

// Elvis（艾维斯，猫王。?:像猫王的表情符号） 操作符
val len = nullableName?.length ?: 0

// 非空断言（谨慎使用）
val definiteLength = nullableName!!.length
```

### Q4: 什么是数据类？有什么特点？

**答案**：
- **定义**：专门用于保存数据的类
- **特点**：自动生成 equals、hashCode、toString、copy 等方法

```kotlin
data class User(val id: Int, val name: String, val email: String)

val user1 = User(1, "Alice", "<EMAIL>")
val user2 = user1.copy(name = "Bob") // 复制并修改部分属性

println(user1) // User(id=1, name=Alice, email=<EMAIL>)
println(user1 == user2) // false
```

### Q5: 扩展函数的原理是什么？

**答案**：
- **原理**：编译时转换为静态方法，接收者作为第一个参数
- **优势**：为现有类添加功能而不修改源码

```kotlin
// 扩展函数
fun String.isPalindrome(): Boolean {
    return this == this.reversed()
}

// 编译后等价于
public static boolean isPalindrome(String $this) {
    return $this.equals(new StringBuilder($this).reverse().toString());
}
```

## 2. 面向对象类

### Q6: Kotlin 中的继承规则？

**答案**：
- 类默认是 final 的，需要 open 关键字才能被继承
- 方法默认是 final 的，需要 open 关键字才能被重写

```kotlin
open class Animal {
    open fun makeSound() {
        println("Some sound")
    }
}

class Dog : Animal() {
    override fun makeSound() {
        println("Woof!")
    }
}
```

### Q7: 什么是密封类？使用场景？

**答案**：
- **定义**：限制类层次结构的抽象类
- **特点**：所有子类必须在同一文件中定义
- **用途**：表示受限的类层次结构，常用于状态管理

```kotlin
sealed class Result<out T> {
    data class Success<T>(val data: T) : Result<T>()
    data class Error(val exception: Throwable) : Result<Nothing>()
    object Loading : Result<Nothing>()
}

fun handleResult(result: Result<String>) {
    when (result) {
        is Result.Success -> println("Data: ${result.data}")
        is Result.Error -> println("Error: ${result.exception}")
        Result.Loading -> println("Loading...")
        // 不需要 else 分支，编译器保证完整性
    }
}
```

### Q8: object 关键字的用法？

**答案**：
1. **单例对象**
2. **对象表达式**（匿名对象）
3. **伴生对象**

```kotlin
// 1. 单例对象
object DatabaseManager {
    fun connect() { /* ... */ }
}

// 2. 对象表达式
val clickListener = object : OnClickListener {
    override fun onClick(view: View) { /* ... */ }
}

// 3. 伴生对象
class MyClass {
    companion object {
        const val CONSTANT = "value"
        fun create(): MyClass = MyClass()
    }
}
```

## 3. 函数式编程类

### Q9: 高阶函数是什么？

**答案**：
- **定义**：接受函数作为参数或返回函数的函数
- **优势**：代码复用、函数组合

```kotlin
// 高阶函数
fun processNumbers(numbers: List<Int>, operation: (Int) -> Int): List<Int> {
    return numbers.map(operation)
}

// 使用
val doubled = processNumbers(listOf(1, 2, 3)) { it * 2 }
val squared = processNumbers(listOf(1, 2, 3)) { it * it }
```

### Q10: 作用域函数的区别？

**答案**：

| 函数 | 对象引用 | 返回值 | 使用场景 |
|------|----------|--------|----------|
| let | it | Lambda 结果 | 空值处理、变量作用域限制 |
| run | this | Lambda 结果 | 对象配置和计算结果 |
| apply | this | 对象本身 | 对象配置 |
| also | it | 对象本身 | 附加操作 |
| with | this | Lambda 结果 | 多个操作 |

```kotlin
val person = Person("Alice", 25).apply {
    age = 26
    // 配置对象
}.also {
    println("Created person: $it")
}.let {
    // 进一步处理
    it.name.uppercase()
}
```

## 4. 集合操作类

### Q11: Kotlin 集合的特点？

**答案**：
- **不可变集合**：listOf、setOf、mapOf
- **可变集合**：mutableListOf、mutableSetOf、mutableMapOf
- **丰富的操作符**：map、filter、reduce 等

```kotlin
val numbers = listOf(1, 2, 3, 4, 5)

val evenNumbers = numbers.filter { it % 2 == 0 }
val doubled = numbers.map { it * 2 }
val sum = numbers.reduce { acc, n -> acc + n }
val grouped = numbers.groupBy { it % 3 }
```

### Q12: 序列（Sequence）和集合的区别？

**答案**：
- **集合**：立即执行（eager evaluation）
- **序列**：延迟执行（lazy evaluation）

```kotlin
// 集合：每个操作都会创建新的集合
val result1 = listOf(1, 2, 3, 4, 5)
    .map { it * 2 }     // 创建新集合 [2, 4, 6, 8, 10]
    .filter { it > 5 }  // 创建新集合 [6, 8, 10]

// 序列：延迟执行，只在终端操作时执行
val result2 = listOf(1, 2, 3, 4, 5)
    .asSequence()
    .map { it * 2 }     // 不执行
    .filter { it > 5 }  // 不执行
    .toList()           // 现在才执行所有操作
```

## 5. 泛型类

### Q13: Kotlin 泛型的协变和逆变？

**答案**：
- **协变（out）**：只能作为输出，不能作为输入
- **逆变（in）**：只能作为输入，不能作为输出

```kotlin
// 协变
interface Producer<out T> {
    fun produce(): T
    // fun consume(item: T) // ❌ 不允许
}

// 逆变
interface Consumer<in T> {
    fun consume(item: T)
    // fun produce(): T // ❌ 不允许
}

// 使用
val stringProducer: Producer<String> = object : Producer<String> {
    override fun produce(): String = "Hello"
}
val anyProducer: Producer<Any> = stringProducer // ✅ 协变允许
```

### Q14: 星号投影（*）是什么？

**答案**：
- **定义**：表示不知道泛型参数的具体类型
- **用途**：当不关心泛型参数时使用

```kotlin
fun printList(list: List<*>) {
    for (item in list) {
        println(item) // item 类型是 Any?
    }
}

// 等价于
fun printList(list: List<out Any?>) {
    // ...
}
```

## 6. 实际应用类

### Q15: 如何在 Android 中使用 Kotlin？

**答案**：
- **空安全**：减少 NPE 崩溃
- **扩展函数**：简化 Android API 使用
- **协程**：替代 AsyncTask
- **数据类**：简化数据模型

```kotlin
// 扩展函数简化 Toast
fun Context.toast(message: String) {
    Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
}

// 使用
activity.toast("Hello Kotlin!")

// 协程处理异步操作
class MainActivity : AppCompatActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        lifecycleScope.launch {
            val data = withContext(Dispatchers.IO) {
                fetchDataFromNetwork()
            }
            updateUI(data)
        }
    }
}
```

## 7. 面试技巧

### 回答策略：
1. **先说概念**：简洁明了地解释是什么
2. **举例说明**：用代码示例证明理解
3. **对比分析**：与 Java 或其他语言对比
4. **实际应用**：说明在项目中如何使用
5. **注意事项**：提及使用时的注意点

### 常见追问：
- "你在项目中是如何使用的？"
- "遇到过什么问题？如何解决的？"
- "为什么选择 Kotlin 而不是 Java？"
- "Kotlin 有什么缺点？"

### 加分回答：
- 提及 Kotlin 的发展历史和未来规划
- 讨论 Kotlin Multiplatform
- 分享学习 Kotlin 的心得体会
- 展示对 Kotlin 生态的了解
