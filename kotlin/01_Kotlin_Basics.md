# Kotlin 基础语法和特性

## 1. Kotlin 简介

### 什么是 Kotlin？
**通俗解释**：Kotlin 就像是 Java 的升级版，保留了 Java 的优点，同时解决了 Java 的很多痛点。

**专业解释**：Kotlin 是 JetBrains 开发的静态类型编程语言，100% 兼容 Java，可以编译为 JVM 字节码、Android、JavaScript 和 Native 代码。

### 为什么选择 Kotlin？
- **简洁性**：减少样板代码
- **安全性**：空安全、类型安全
- **互操作性**：与 Java 100% 兼容
- **表达性**：更具表达力的语法

## 2. 基础语法

### 2.1 变量声明

```kotlin
// 可变变量
var name: String = "Kotlin"
var age = 25  // 类型推断

// 不可变变量（推荐）
val language: String = "Kotlin"
val version = "1.9"  // 类型推断

// 延迟初始化
lateinit var database: Database
```

**通俗解释**：
- `var` 就像是可以换内容的盒子
- `val` 就像是密封的盒子，一旦放入就不能更换

### 2.2 空安全

```kotlin
// 可空类型
var nullableName: String? = null
var nonNullName: String = "Kotlin"

// 安全调用
val length = nullableName?.length

// Elvis 操作符
val nameLength = nullableName?.length ?: 0

// 非空断言（谨慎使用）
val definiteLength = nullableName!!.length
```

**通俗解释**：Kotlin 的空安全就像是给每个变量都加了一个"可能为空"的标签，编译器会提醒你处理空值情况。

### 2.3 函数定义

```kotlin
// 基本函数
fun greet(name: String): String {
    return "Hello, $name!"
}

// 单表达式函数
fun add(a: Int, b: Int) = a + b

// 默认参数
fun createUser(name: String, age: Int = 18, city: String = "Beijing") {
    // ...
}

// 命名参数
createUser(name = "Alice", city = "Shanghai")
```

### 2.4 字符串模板

```kotlin
val name = "Kotlin"
val version = 1.9

// 简单插值
val message = "Welcome to $name"

// 表达式插值
val info = "Current version is ${version.toString()}"

// 多行字符串
val multiLine = """
    |This is a
    |multi-line
    |string
""".trimMargin()
```

## 3. 类和对象

### 3.1 类定义

```kotlin
// 基本类
class Person(val name: String, var age: Int) {
    
    // 次构造函数
    constructor(name: String) : this(name, 0)
    
    // 方法
    fun introduce() {
        println("Hi, I'm $name, $age years old")
    }
}

// 数据类
data class User(val id: Int, val name: String, val email: String)

// 密封类
sealed class Result {
    data class Success(val data: String) : Result()
    data class Error(val exception: Throwable) : Result()
    object Loading : Result()
}
```

### 3.2 继承和接口

```kotlin
// 接口
interface Drawable {
    fun draw()
    
    // 默认实现
    fun getInfo() = "This is drawable"
}

// 抽象类
abstract class Shape : Drawable {
    abstract val area: Double
}

// 具体实现
class Circle(private val radius: Double) : Shape() {
    override val area: Double
        get() = Math.PI * radius * radius
    
    override fun draw() {
        println("Drawing a circle")
    }
}
```

## 4. 集合操作

### 4.1 集合创建

```kotlin
// 不可变集合
val numbers = listOf(1, 2, 3, 4, 5)
val map = mapOf("a" to 1, "b" to 2)
val set = setOf(1, 2, 3)

// 可变集合
val mutableList = mutableListOf(1, 2, 3)
val mutableMap = mutableMapOf("a" to 1)
```

### 4.2 集合操作

```kotlin
val numbers = listOf(1, 2, 3, 4, 5, 6, 7, 8, 9, 10)

// 过滤
val evenNumbers = numbers.filter { it % 2 == 0 }

// 映射
val doubled = numbers.map { it * 2 }

// 查找
val firstEven = numbers.find { it % 2 == 0 }

// 分组
val grouped = numbers.groupBy { it % 3 }

// 聚合
val sum = numbers.sum()
val max = numbers.maxOrNull()
```

## 5. 扩展函数

```kotlin
// 为 String 添加扩展函数
fun String.isPalindrome(): Boolean {
    return this == this.reversed()
}

// 使用扩展函数
val text = "level"
println(text.isPalindrome()) // true

// 为泛型添加扩展
fun <T> List<T>.secondOrNull(): T? {
    return if (size >= 2) this[1] else null
}
```

**通俗解释**：扩展函数就像是给现有的类"外挂"新功能，不需要修改原始类的代码。

## 6. 高阶函数和 Lambda

```kotlin
// 高阶函数
fun processNumbers(numbers: List<Int>, operation: (Int) -> Int): List<Int> {
    return numbers.map(operation)
}

// 使用 Lambda
val doubled = processNumbers(listOf(1, 2, 3)) { it * 2 }

// 函数类型参数
fun calculate(x: Int, y: Int, operation: (Int, Int) -> Int): Int {
    return operation(x, y)
}

val result = calculate(5, 3) { a, b -> a + b }
```

## 7. 作用域函数

```kotlin
// let - 对象配置和空值处理
val result = "Hello".let {
    println(it)
    it.length
}

// apply - 对象配置
val person = Person("Alice", 25).apply {
    age = 26
    // 其他配置
}

// run - 对象配置和计算结果
val length = "Hello".run {
    println("Processing: $this")
    length
}

// also - 附加操作
val numbers = mutableListOf(1, 2, 3).also {
    println("Created list: $it")
}

// with - 多个操作
val result = with(StringBuilder()) {
    append("Hello")
    append(" ")
    append("World")
    toString()
}
```

## 8. 面试重点

### 常见面试问题：

1. **Kotlin 相比 Java 有什么优势？**
   - 空安全、简洁语法、函数式编程支持、协程等

2. **什么是空安全？如何实现的？**
   - 类型系统区分可空和非空类型，编译时检查

3. **data class 有什么特点？**
   - 自动生成 equals、hashCode、toString、copy 等方法

4. **扩展函数的原理是什么？**
   - 编译时转换为静态方法，第一个参数是接收者对象

5. **作用域函数的区别？**
   - 返回值和上下文对象的引用方式不同

### 最佳实践：
- 优先使用 `val` 而不是 `var`
- 合理使用空安全操作符
- 善用数据类和密封类
- 利用扩展函数增强代码可读性
- 使用作用域函数简化代码

## 9. 与 Java 对比

| 特性 | Java | Kotlin |
|------|------|--------|
| 空安全 | 需要手动检查 | 编译时保证 |
| 样板代码 | 较多 | 极少 |
| 函数式编程 | Java 8+ 支持 | 原生支持 |
| 扩展方法 | 不支持 | 支持 |
| 协程 | 需要第三方库 | 原生支持 |

```java
// Java 代码
public class Person {
    private String name;
    private int age;

    public Person(String name, int age) {
        this.name = name;
        this.age = age;
    }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    public int getAge() { return age; }
    public void setAge(int age) { this.age = age; }
}
```

```kotlin
// Kotlin 等价代码
data class Person(var name: String, var age: Int)
```
