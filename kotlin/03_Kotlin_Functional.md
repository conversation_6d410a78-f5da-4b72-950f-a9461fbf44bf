# Kotlin 函数式编程

## 1. 函数基础

### 1.1 函数定义

```kotlin
// 基本函数
fun greet(name: String): String {
    return "Hello, $name!"
}

// 单表达式函数
fun add(a: Int, b: Int) = a + b

// 无返回值函数
fun printMessage(message: String): Unit {
    println(message)
}

// Unit 可以省略
fun printMessage2(message: String) {
    println(message)
}
```

### 1.2 函数参数

```kotlin
// 默认参数
fun createUser(
    name: String,
    age: Int = 18,
    city: String = "Beijing",
    isActive: Boolean = true
) {
    println("User: $name, $age, $city, active: $isActive")
}

// 命名参数
fun namedParametersDemo() {
    createUser("Alice")
    createUser("Bob", age = 25)
    createUser(name = "Charlie", city = "Shanghai", age = 30)
}

// 可变参数
fun sum(vararg numbers: Int): Int {
    return numbers.sum()
}

fun varargDemo() {
    println(sum(1, 2, 3, 4, 5))
    
    val array = intArrayOf(1, 2, 3)
    println(sum(*array)) // 展开操作符
}
```

## 2. 高阶函数

### 2.1 函数类型

```kotlin
// 函数类型声明
val operation: (Int, Int) -> Int = { a, b -> a + b }

// 可空函数类型
val nullableOperation: ((Int, Int) -> Int)? = null

// 带接收者的函数类型
val stringOperation: String.(Int) -> String = { length ->
    this.take(length)
}
```

### 2.2 高阶函数定义

```kotlin
// 接受函数作为参数
fun processNumbers(
    numbers: List<Int>,
    operation: (Int) -> Int
): List<Int> {
    return numbers.map(operation)
}

// 返回函数
fun createMultiplier(factor: Int): (Int) -> Int {
    return { number -> number * factor }
}

// 使用示例
fun higherOrderDemo() {
    val numbers = listOf(1, 2, 3, 4, 5)
    
    // 传递 Lambda
    val doubled = processNumbers(numbers) { it * 2 }
    val squared = processNumbers(numbers) { it * it }
    
    println("Doubled: $doubled")
    println("Squared: $squared")
    
    // 使用返回的函数
    val multiplyBy3 = createMultiplier(3)
    val result = multiplyBy3(10)
    println("10 * 3 = $result")
}
```

## 3. Lambda 表达式

### 3.1 Lambda 语法

```kotlin
// 完整语法
val lambda1: (Int, Int) -> Int = { a: Int, b: Int -> a + b }

// 类型推断
val lambda2 = { a: Int, b: Int -> a + b }

// 单参数 it
val lambda3: (Int) -> Int = { it * 2 }

// 多行 Lambda
val lambda4 = { numbers: List<Int> ->
    val sum = numbers.sum()
    val average = sum.toDouble() / numbers.size
    "Sum: $sum, Average: $average"
}
```

### 3.2 Lambda 使用场景

```kotlin
fun lambdaUseCases() {
    val numbers = listOf(1, 2, 3, 4, 5, 6, 7, 8, 9, 10)
    
    // 过滤
    val evenNumbers = numbers.filter { it % 2 == 0 }
    
    // 映射
    val doubled = numbers.map { it * 2 }
    
    // 查找
    val firstEven = numbers.find { it % 2 == 0 }
    val firstOrNull = numbers.firstOrNull { it > 5 }
    
    // 分组
    val grouped = numbers.groupBy { it % 3 }
    
    // 排序
    val sorted = numbers.sortedBy { -it } // 降序
    
    // 聚合
    val sum = numbers.reduce { acc, n -> acc + n }
    val product = numbers.fold(1) { acc, n -> acc * n }
    
    println("Even: $evenNumbers")
    println("Doubled: $doubled")
    println("First even: $firstEven")
    println("Grouped: $grouped")
}
```

## 4. 内联函数

### 4.1 内联函数基础

```kotlin
// 内联函数避免 Lambda 的对象创建开销
inline fun measureTime(action: () -> Unit): Long {
    val startTime = System.currentTimeMillis()
    action()
    val endTime = System.currentTimeMillis()
    return endTime - startTime
}

// 使用
fun inlineDemo() {
    val time = measureTime {
        // 一些耗时操作
        Thread.sleep(100)
    }
    println("Time taken: ${time}ms")
}
```

### 4.2 noinline 和 crossinline

```kotlin
// noinline - 防止特定参数被内联
inline fun processData(
    data: List<Int>,
    noinline logger: (String) -> Unit,
    processor: (Int) -> Int
) {
    logger("Processing ${data.size} items")
    data.map(processor)
}

// crossinline - 防止非局部返回
inline fun runWithCallback(crossinline callback: () -> Unit) {
    Thread {
        callback() // 不能包含 return
    }.start()
}
```

## 5. 函数式集合操作

### 5.1 转换操作

```kotlin
fun transformationOperations() {
    val numbers = listOf(1, 2, 3, 4, 5)
    
    // map - 一对一转换
    val doubled = numbers.map { it * 2 }
    
    // flatMap - 一对多转换
    val words = listOf("hello", "world")
    val letters = words.flatMap { it.toList() }
    
    // mapIndexed - 带索引的转换
    val indexed = numbers.mapIndexed { index, value -> 
        "[$index] = $value" 
    }
    
    // mapNotNull - 过滤空值
    val strings = listOf("1", "2", "abc", "3")
    val validNumbers = strings.mapNotNull { it.toIntOrNull() }
    
    println("Doubled: $doubled")
    println("Letters: $letters")
    println("Indexed: $indexed")
    println("Valid numbers: $validNumbers")
}
```

### 5.2 过滤操作

```kotlin
fun filteringOperations() {
    val numbers = listOf(1, 2, 3, 4, 5, 6, 7, 8, 9, 10)
    
    // filter - 基本过滤
    val evenNumbers = numbers.filter { it % 2 == 0 }
    
    // filterNot - 反向过滤
    val oddNumbers = numbers.filterNot { it % 2 == 0 }
    
    // filterIndexed - 带索引过滤
    val filteredByIndex = numbers.filterIndexed { index, _ -> 
        index % 2 == 0 
    }
    
    // partition - 分割
    val (evens, odds) = numbers.partition { it % 2 == 0 }
    
    // take/drop - 取前/后几个
    val firstThree = numbers.take(3)
    val withoutFirstThree = numbers.drop(3)
    
    // takeWhile/dropWhile - 条件取/丢
    val takeWhileSmall = numbers.takeWhile { it < 5 }
    val dropWhileSmall = numbers.dropWhile { it < 5 }
    
    println("Even: $evenNumbers")
    println("Odd: $oddNumbers")
    println("Evens/Odds: $evens / $odds")
    println("Take while < 5: $takeWhileSmall")
}
```

### 5.3 聚合操作

```kotlin
fun aggregationOperations() {
    val numbers = listOf(1, 2, 3, 4, 5)
    val words = listOf("apple", "banana", "cherry")
    
    // 基本聚合
    val sum = numbers.sum()
    val average = numbers.average()
    val max = numbers.maxOrNull()
    val min = numbers.minOrNull()
    
    // reduce - 累积操作
    val product = numbers.reduce { acc, n -> acc * n }
    
    // fold - 带初始值的累积
    val sumWithInitial = numbers.fold(10) { acc, n -> acc + n }
    
    // 字符串聚合
    val concatenated = words.reduce { acc, word -> "$acc, $word" }
    val joined = words.joinToString(separator = " | ")
    
    // 条件聚合
    val maxByLength = words.maxByOrNull { it.length }
    val minByLength = words.minByOrNull { it.length }
    
    println("Sum: $sum, Average: $average")
    println("Product: $product")
    println("Sum with initial: $sumWithInitial")
    println("Concatenated: $concatenated")
    println("Joined: $joined")
    println("Max by length: $maxByLength")
}
```

## 6. 序列 (Sequence)

### 6.1 序列的优势

```kotlin
fun sequenceDemo() {
    val numbers = (1..1000000).toList()
    
    // 集合操作 - 立即执行，创建中间集合
    val listResult = numbers
        .filter { it % 2 == 0 }
        .map { it * it }
        .take(10)
        .toList()
    
    // 序列操作 - 延迟执行，不创建中间集合
    val sequenceResult = numbers.asSequence()
        .filter { it % 2 == 0 }
        .map { it * it }
        .take(10)
        .toList()
    
    println("List result: $listResult")
    println("Sequence result: $sequenceResult")
}
```

### 6.2 序列创建

```kotlin
fun sequenceCreation() {
    // 从集合创建
    val fromList = listOf(1, 2, 3).asSequence()
    
    // 生成序列
    val generated = generateSequence(1) { it + 1 }
        .take(10)
        .toList()
    
    // 斐波那契数列
    val fibonacci = generateSequence(Pair(0, 1)) { (a, b) -> 
        Pair(b, a + b) 
    }.map { it.first }
        .take(10)
        .toList()
    
    // 无限序列
    val infiniteSequence = generateSequence(0) { it + 2 }
    val firstTenEvens = infiniteSequence.take(10).toList()
    
    println("Generated: $generated")
    println("Fibonacci: $fibonacci")
    println("First ten evens: $firstTenEvens")
}
```

## 7. 函数组合

### 7.1 函数组合操作

```kotlin
// 函数组合
infix fun <A, B, C> ((A) -> B).andThen(f: (B) -> C): (A) -> C {
    return { a -> f(this(a)) }
}

infix fun <A, B, C> ((B) -> C).compose(f: (A) -> B): (A) -> C {
    return { a -> this(f(a)) }
}

fun functionComposition() {
    val addOne: (Int) -> Int = { it + 1 }
    val multiplyByTwo: (Int) -> Int = { it * 2 }
    val toString: (Int) -> String = { it.toString() }
    
    // 函数组合
    val composed = addOne andThen multiplyByTwo andThen toString
    val result = composed(5) // ((5 + 1) * 2).toString() = "12"
    
    println("Composed result: $result")
}
```

### 7.2 柯里化

```kotlin
// 柯里化函数
fun <A, B, C> curry(f: (A, B) -> C): (A) -> (B) -> C {
    return { a -> { b -> f(a, b) } }
}

fun curryingDemo() {
    val add = { a: Int, b: Int -> a + b }
    val curriedAdd = curry(add)
    
    val addFive = curriedAdd(5)
    val result = addFive(3) // 8
    
    println("Curried result: $result")
}
```

## 8. 实际应用示例

### 8.1 数据处理管道

```kotlin
data class Student(val name: String, val age: Int, val grade: Double)

fun dataProcessingPipeline() {
    val students = listOf(
        Student("Alice", 20, 85.5),
        Student("Bob", 19, 92.0),
        Student("Charlie", 21, 78.5),
        Student("Diana", 20, 95.5),
        Student("Eve", 19, 88.0)
    )
    
    // 函数式数据处理管道
    val topStudents = students
        .filter { it.grade >= 85.0 }
        .sortedByDescending { it.grade }
        .take(3)
        .map { "${it.name} (${it.grade})" }
        .joinToString(", ")
    
    println("Top students: $topStudents")
    
    // 分组统计
    val gradeStats = students
        .groupBy { it.age }
        .mapValues { (_, studentsOfAge) ->
            studentsOfAge.map { it.grade }.average()
        }
    
    println("Average grade by age: $gradeStats")
}
```

### 8.2 函数式错误处理

```kotlin
sealed class Result<out T> {
    data class Success<T>(val value: T) : Result<T>()
    data class Failure(val error: String) : Result<Nothing>()
}

fun <T, R> Result<T>.map(transform: (T) -> R): Result<R> {
    return when (this) {
        is Result.Success -> Result.Success(transform(value))
        is Result.Failure -> this
    }
}

fun <T, R> Result<T>.flatMap(transform: (T) -> Result<R>): Result<R> {
    return when (this) {
        is Result.Success -> transform(value)
        is Result.Failure -> this
    }
}

fun functionalErrorHandling() {
    fun parseNumber(str: String): Result<Int> {
        return try {
            Result.Success(str.toInt())
        } catch (e: NumberFormatException) {
            Result.Failure("Invalid number: $str")
        }
    }
    
    fun divide(a: Int, b: Int): Result<Double> {
        return if (b != 0) {
            Result.Success(a.toDouble() / b)
        } else {
            Result.Failure("Division by zero")
        }
    }
    
    // 函数式错误处理链
    val result = parseNumber("10")
        .flatMap { a ->
            parseNumber("2").flatMap { b ->
                divide(a, b)
            }
        }
        .map { "Result: $it" }
    
    when (result) {
        is Result.Success -> println(result.value)
        is Result.Failure -> println("Error: ${result.error}")
    }
}
```

## 9. 面试重点

### 常见面试问题：

1. **什么是高阶函数？**
   - 接受函数作为参数或返回函数的函数

2. **Lambda 表达式的优势？**
   - 简洁的语法、函数式编程支持、减少样板代码

3. **内联函数的作用？**
   - 避免 Lambda 的对象创建开销，提高性能

4. **序列和集合的区别？**
   - 序列延迟执行，集合立即执行

5. **函数式编程的优势？**
   - 代码简洁、易于测试、减少副作用、支持并行处理

### 最佳实践：
- 优先使用不可变数据
- 善用集合操作符简化代码
- 合理使用序列优化性能
- 利用函数组合构建复杂逻辑
- 使用函数式错误处理模式
