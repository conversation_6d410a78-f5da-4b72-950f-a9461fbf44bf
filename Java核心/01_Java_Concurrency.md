# 01 - Java 并发编程核心

## 概述

Java 并发编程是指在多线程环境中，协调多个任务同时执行，以充分利用现代多核 CPU 的计算能力。掌握并发编程是衡量一个 Java 开发者水平的重要标准，也是构建高性能、高响应性应用的基础。

---

## 1. 线程 (Thread)

-   **定义**: 线程是操作系统能够进行运算调度的最小单位，它被包含在进程之中。
-   **创建方式**:
    1.  继承 `Thread` 类并重写 `run()` 方法。
    2.  实现 `Runnable` 接口并将其作为 `Thread` 的构造函数参数（**推荐，因为 Java 是单继承**）。
    3.  实现 `Callable` 接口（可以有返回值，并能抛出异常），并与 `FutureTask` 结合使用。

### Demo: 创建并启动线程

```java
public class ThreadCreationDemo {
    // 方式一：继承 Thread
    static class MyThread extends Thread {
        @Override
        public void run() {
            System.out.println("Hello from MyThread!");
        }
    }

    // 方式二：实现 Runnable
    static class MyRunnable implements Runnable {
        @Override
        public void run() {
            System.out.println("Hello from MyRunnable!");
        }
    }

    public static void main(String[] args) {
        MyThread t1 = new MyThread();
        t1.start();

        Thread t2 = new Thread(new MyRunnable());
        t2.start();
    }
}
```

---

## 2. 线程池 (Executor Framework)

-   **为什么需要线程池？**: 频繁地创建和销毁线程会带来巨大的性能开销。线程池通过复用已创建的线程，来降低资源消耗、提高响应速度并方便管理。
-   **核心组件**: `ExecutorService` 接口。
-   **创建方式**: 通常通过 `Executors` 工厂类来创建。
    -   `newFixedThreadPool(int n)`: 创建一个固定大小的线程池。
    -   `newCachedThreadPool()`: 创建一个可缓存的线程池，线程数动态调整。
    -   `newSingleThreadExecutor()`: 创建一个单线程的线程池。

### Demo: 使用固定大小的线程池

```java
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

public class ThreadPoolDemo {
    public static void main(String[] args) throws InterruptedException {
        // 创建一个包含2个线程的线程池
        ExecutorService executor = Executors.newFixedThreadPool(2);

        // 提交5个任务到线程池
        for (int i = 0; i < 5; i++) {
            final int taskId = i;
            executor.submit(() -> {
                System.out.println("Executing task " + taskId + " by " + Thread.currentThread().getName());
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            });
        }

        // 关闭线程池
        executor.shutdown(); // 不再接受新任务，但会等待已提交任务完成
        executor.awaitTermination(5, TimeUnit.SECONDS); // 等待线程池终止
        System.out.println("All tasks finished.");
    }
}
```

---

## 3. 锁与同步 (Lock & Synchronization)

当多个线程访问共享资源时，为了防止数据错乱，需要使用锁来保证**原子性**和**可见性**。

-   **`synchronized` 关键字**: Java 内置的锁机制，可以修饰方法或代码块。它是一种**可重入**的**悲观锁**。
-   **`Lock` 接口**: `java.util.concurrent.locks` 包提供了更灵活的锁实现，如 `ReentrantLock`。
    -   **相比 `synchronized` 的优点**: 可中断、可超时、可实现公平锁、可绑定多个条件 (`Condition`)。

### Demo: 使用 `synchronized` 和 `ReentrantLock` 保证线程安全

```java
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

class Counter {
    private int count = 0;
    private final Lock lock = new ReentrantLock();

    // 使用 synchronized 关键字
    public synchronized void incrementSync() {
        count++;
    }

    // 使用 ReentrantLock
    public void incrementLock() {
        lock.lock();
        try {
            count++;
        } finally {
            lock.unlock(); // 必须在 finally 块中释放锁
        }
    }

    public int getCount() {
        return count;
    }
}
```

---

## 4. 常见并发工具 (java.util.concurrent)

-   **`CountDownLatch`**: 允许一个或多个线程等待其他一组线程完成操作。像一个“倒数计数器”。
-   **`CyclicBarrier`**: 让一组线程到达一个屏障时被阻塞，直到最后一个线程到达屏障时，所有线程才会继续执行。可以循环使用。
-   **`Semaphore`**: 信号量，用于控制同时访问特定资源的线程数量，常用于流量控制。
-   **`ConcurrentHashMap`**: 线程安全的哈希表，是面试中的高频考点。在 JDK 1.8 中通过 `CAS + synchronized` 实现了更细粒度的锁，性能很高。
-   **`volatile` 关键字**: 保证了变量在多线程之间的**可见性**，但不保证原子性。它能确保一个线程对变量的修改能立刻被其他线程看到。
