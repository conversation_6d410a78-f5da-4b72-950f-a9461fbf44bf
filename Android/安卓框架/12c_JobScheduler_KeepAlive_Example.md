# JobScheduler保活实现示例

## 概述

JobScheduler是Android 5.0 (API 21)引入的任务调度框架，可以在满足特定条件时执行后台任务。它是系统级的服务，即使应用被杀死也能在条件满足时重新启动任务。

---

## 1. JobService实现

### 1.1 基础JobService

```kotlin
@TargetApi(Build.VERSION_CODES.LOLLIPOP)
class KeepAliveJobService : JobService() {
    
    companion object {
        private const val JOB_ID = 1000
        private const val TAG = "KeepAliveJobService"
    }
    
    override fun onStartJob(params: JobParameters?): Boolean {
        Log.d(TAG, "JobService started with ID: ${params?.jobId}")
        
        // 在后台线程执行保活逻辑
        Thread {
            try {
                performKeepAliveTask(params)
            } catch (e: Exception) {
                Log.e(TAG, "保活任务执行失败", e)
            } finally {
                // 任务完成后调用jobFinished
                jobFinished(params, false)
                
                // 重新调度下一次任务
                scheduleNextJob()
            }
        }.start()
        
        // 返回true表示任务在后台线程中执行
        return true
    }
    
    override fun onStopJob(params: JobParameters?): Boolean {
        Log.d(TAG, "JobService stopped with ID: ${params?.jobId}")
        
        // 返回true表示需要重新调度任务
        return true
    }
    
    private fun performKeepAliveTask(params: JobParameters?) {
        Log.d(TAG, "执行保活任务")
        
        // 1. 检查关键服务是否运行
        checkAndRestartServices()
        
        // 2. 发送心跳包
        sendHeartbeat()
        
        // 3. 检查应用状态
        checkAppStatus()
        
        // 4. 清理内存
        performMemoryCleanup()
        
        // 5. 更新保活统计
        updateKeepAliveStats()
        
        // 模拟任务执行时间
        Thread.sleep(2000)
        
        Log.d(TAG, "保活任务执行完成")
    }
    
    private fun checkAndRestartServices() {
        // 检查KeepAliveService是否运行
        if (!isServiceRunning(KeepAliveService::class.java)) {
            Log.w(TAG, "KeepAliveService未运行，尝试重启")
            val intent = Intent(this, KeepAliveService::class.java)
            startForegroundService(intent)
        }
        
        // 检查其他关键服务
        if (!isServiceRunning(MainProcessService::class.java)) {
            Log.w(TAG, "MainProcessService未运行，尝试重启")
            val intent = Intent(this, MainProcessService::class.java)
            startService(intent)
        }
    }
    
    private fun sendHeartbeat() {
        try {
            // 发送心跳包到服务器
            HeartbeatManager.getInstance().sendHeartbeat()
        } catch (e: Exception) {
            Log.e(TAG, "发送心跳包失败", e)
        }
    }
    
    private fun checkAppStatus() {
        // 检查应用各模块状态
        val processInfo = ProcessPriorityManager.getProcessInfo(this)
        Log.d(TAG, "进程状态: $processInfo")
        
        // 检查内存使用情况
        MemoryOptimizationManager.monitorMemoryUsage(this)
    }
    
    private fun performMemoryCleanup() {
        // 执行内存清理
        MemoryOptimizationManager.optimizeMemoryUsage(this)
    }
    
    private fun updateKeepAliveStats() {
        // 更新保活统计信息
        val sharedPrefs = getSharedPreferences("keep_alive_stats", Context.MODE_PRIVATE)
        val currentCount = sharedPrefs.getInt("job_execution_count", 0)
        sharedPrefs.edit()
            .putInt("job_execution_count", currentCount + 1)
            .putLong("last_execution_time", System.currentTimeMillis())
            .apply()
    }
    
    private fun scheduleNextJob() {
        JobSchedulerHelper.scheduleKeepAliveJob(this)
    }
    
    private fun isServiceRunning(serviceClass: Class<*>): Boolean {
        val manager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        return manager.getRunningServices(Integer.MAX_VALUE).any { 
            it.service.className == serviceClass.name 
        }
    }
}
```

### 1.2 JobScheduler辅助类

```kotlin
@TargetApi(Build.VERSION_CODES.LOLLIPOP)
class JobSchedulerHelper {
    
    companion object {
        private const val KEEP_ALIVE_JOB_ID = 1000
        private const val PERIODIC_JOB_ID = 1001
        private const val NETWORK_JOB_ID = 1002
        private const val CHARGING_JOB_ID = 1003
        
        fun scheduleKeepAliveJob(context: Context) {
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
                Log.w("JobSchedulerHelper", "JobScheduler需要API 21+")
                return
            }
            
            val jobScheduler = context.getSystemService(Context.JOB_SCHEDULER_SERVICE) as JobScheduler
            
            // 取消之前的任务
            jobScheduler.cancel(KEEP_ALIVE_JOB_ID)
            
            val jobInfo = JobInfo.Builder(KEEP_ALIVE_JOB_ID, ComponentName(context, KeepAliveJobService::class.java))
                .setMinimumLatency(30 * 1000) // 最小延迟30秒
                .setOverrideDeadline(60 * 1000) // 最大延迟60秒
                .setRequiredNetworkType(JobInfo.NETWORK_TYPE_NONE) // 不要求网络
                .setRequiresCharging(false) // 不要求充电
                .setRequiresDeviceIdle(false) // 不要求设备空闲
                .setPersisted(true) // 重启后保持
                .build()
            
            val result = jobScheduler.schedule(jobInfo)
            if (result == JobScheduler.RESULT_SUCCESS) {
                Log.d("JobSchedulerHelper", "保活任务调度成功")
            } else {
                Log.e("JobSchedulerHelper", "保活任务调度失败")
            }
        }
        
        fun schedulePeriodicJob(context: Context) {
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) return
            
            val jobScheduler = context.getSystemService(Context.JOB_SCHEDULER_SERVICE) as JobScheduler
            
            val jobInfo = JobInfo.Builder(PERIODIC_JOB_ID, ComponentName(context, KeepAliveJobService::class.java))
                .setPeriodic(15 * 60 * 1000) // 15分钟周期
                .setRequiredNetworkType(JobInfo.NETWORK_TYPE_NONE)
                .setRequiresCharging(false)
                .setRequiresDeviceIdle(false)
                .setPersisted(true)
                .build()
            
            jobScheduler.schedule(jobInfo)
            Log.d("JobSchedulerHelper", "周期性任务调度成功")
        }
        
        fun scheduleNetworkJob(context: Context) {
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) return
            
            val jobScheduler = context.getSystemService(Context.JOB_SCHEDULER_SERVICE) as JobScheduler
            
            val jobInfo = JobInfo.Builder(NETWORK_JOB_ID, ComponentName(context, KeepAliveJobService::class.java))
                .setRequiredNetworkType(JobInfo.NETWORK_TYPE_ANY) // 需要网络连接
                .setRequiresCharging(false)
                .setRequiresDeviceIdle(false)
                .setPersisted(true)
                .build()
            
            jobScheduler.schedule(jobInfo)
            Log.d("JobSchedulerHelper", "网络任务调度成功")
        }
        
        fun scheduleChargingJob(context: Context) {
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) return
            
            val jobScheduler = context.getSystemService(Context.JOB_SCHEDULER_SERVICE) as JobScheduler
            
            val jobInfo = JobInfo.Builder(CHARGING_JOB_ID, ComponentName(context, KeepAliveJobService::class.java))
                .setRequiredNetworkType(JobInfo.NETWORK_TYPE_NONE)
                .setRequiresCharging(true) // 需要充电状态
                .setRequiresDeviceIdle(false)
                .setPersisted(true)
                .build()
            
            jobScheduler.schedule(jobInfo)
            Log.d("JobSchedulerHelper", "充电任务调度成功")
        }
        
        fun cancelAllJobs(context: Context) {
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) return
            
            val jobScheduler = context.getSystemService(Context.JOB_SCHEDULER_SERVICE) as JobScheduler
            jobScheduler.cancelAll()
            Log.d("JobSchedulerHelper", "所有任务已取消")
        }
        
        fun cancelJob(context: Context, jobId: Int) {
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) return
            
            val jobScheduler = context.getSystemService(Context.JOB_SCHEDULER_SERVICE) as JobScheduler
            jobScheduler.cancel(jobId)
            Log.d("JobSchedulerHelper", "任务 $jobId 已取消")
        }
        
        fun getAllPendingJobs(context: Context): List<JobInfo> {
            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                val jobScheduler = context.getSystemService(Context.JOB_SCHEDULER_SERVICE) as JobScheduler
                jobScheduler.allPendingJobs
            } else {
                emptyList()
            }
        }
        
        fun getJobStatus(context: Context): String {
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
                return "JobScheduler不支持当前API版本"
            }
            
            val jobScheduler = context.getSystemService(Context.JOB_SCHEDULER_SERVICE) as JobScheduler
            val pendingJobs = jobScheduler.allPendingJobs
            
            return if (pendingJobs.isEmpty()) {
                "没有待执行的任务"
            } else {
                buildString {
                    appendLine("待执行任务:")
                    pendingJobs.forEach { job ->
                        appendLine("- 任务ID: ${job.id}")
                        appendLine("  服务: ${job.service.className}")
                        appendLine("  网络要求: ${getNetworkTypeDescription(job.networkType)}")
                        appendLine("  需要充电: ${job.isRequireCharging}")
                        appendLine("  需要设备空闲: ${job.isRequireDeviceIdle}")
                        appendLine("  持久化: ${job.isPersisted}")
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                            appendLine("  周期性: ${job.isPeriodic}")
                            if (job.isPeriodic) {
                                appendLine("  间隔: ${job.intervalMillis}ms")
                            }
                        }
                        appendLine()
                    }
                }
            }
        }
        
        private fun getNetworkTypeDescription(networkType: Int): String {
            return when (networkType) {
                JobInfo.NETWORK_TYPE_NONE -> "无网络要求"
                JobInfo.NETWORK_TYPE_ANY -> "任意网络"
                JobInfo.NETWORK_TYPE_UNMETERED -> "非计费网络"
                else -> "未知($networkType)"
            }
        }
    }
}
```

---

## 2. 智能JobScheduler管理器

```kotlin
class SmartJobSchedulerManager(private val context: Context) {
    
    private val sharedPrefs = context.getSharedPreferences("job_scheduler_prefs", Context.MODE_PRIVATE)
    private var failureCount = 0
    private val maxFailureCount = 5
    
    fun startSmartScheduling() {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
            Log.w("SmartJobScheduler", "JobScheduler需要API 21+")
            return
        }
        
        // 根据设备状态选择合适的调度策略
        val strategy = selectSchedulingStrategy()
        applySchedulingStrategy(strategy)
    }
    
    private fun selectSchedulingStrategy(): SchedulingStrategy {
        val batteryLevel = getBatteryLevel()
        val isCharging = isDeviceCharging()
        val isNetworkAvailable = isNetworkAvailable()
        val memoryUsage = getMemoryUsagePercent()
        
        return when {
            batteryLevel < 20 && !isCharging -> {
                // 低电量且未充电：使用节能策略
                SchedulingStrategy.POWER_SAVING
            }
            isCharging && isNetworkAvailable -> {
                // 充电且有网络：使用积极策略
                SchedulingStrategy.AGGRESSIVE
            }
            memoryUsage > 80 -> {
                // 内存使用率高：使用轻量策略
                SchedulingStrategy.LIGHTWEIGHT
            }
            else -> {
                // 正常情况：使用平衡策略
                SchedulingStrategy.BALANCED
            }
        }
    }
    
    private fun applySchedulingStrategy(strategy: SchedulingStrategy) {
        Log.d("SmartJobScheduler", "应用调度策略: $strategy")
        
        // 先取消所有现有任务
        JobSchedulerHelper.cancelAllJobs(context)
        
        when (strategy) {
            SchedulingStrategy.POWER_SAVING -> {
                schedulePowerSavingJobs()
            }
            SchedulingStrategy.LIGHTWEIGHT -> {
                scheduleLightweightJobs()
            }
            SchedulingStrategy.BALANCED -> {
                scheduleBalancedJobs()
            }
            SchedulingStrategy.AGGRESSIVE -> {
                scheduleAggressiveJobs()
            }
        }
        
        // 记录策略应用时间
        sharedPrefs.edit()
            .putString("last_strategy", strategy.name)
            .putLong("strategy_applied_time", System.currentTimeMillis())
            .apply()
    }
    
    private fun schedulePowerSavingJobs() {
        // 节能模式：只在充电时执行任务
        val jobScheduler = context.getSystemService(Context.JOB_SCHEDULER_SERVICE) as JobScheduler
        
        val jobInfo = JobInfo.Builder(2001, ComponentName(context, KeepAliveJobService::class.java))
            .setRequiredNetworkType(JobInfo.NETWORK_TYPE_NONE)
            .setRequiresCharging(true) // 只在充电时执行
            .setRequiresDeviceIdle(false)
            .setPersisted(true)
            .build()
        
        jobScheduler.schedule(jobInfo)
    }
    
    private fun scheduleLightweightJobs() {
        // 轻量模式：减少任务频率
        val jobScheduler = context.getSystemService(Context.JOB_SCHEDULER_SERVICE) as JobScheduler
        
        val jobInfo = JobInfo.Builder(2002, ComponentName(context, KeepAliveJobService::class.java))
            .setMinimumLatency(5 * 60 * 1000) // 5分钟最小延迟
            .setOverrideDeadline(10 * 60 * 1000) // 10分钟最大延迟
            .setRequiredNetworkType(JobInfo.NETWORK_TYPE_NONE)
            .setRequiresCharging(false)
            .setRequiresDeviceIdle(false)
            .setPersisted(true)
            .build()
        
        jobScheduler.schedule(jobInfo)
    }
    
    private fun scheduleBalancedJobs() {
        // 平衡模式：正常频率
        JobSchedulerHelper.scheduleKeepAliveJob(context)
        JobSchedulerHelper.scheduleNetworkJob(context)
    }
    
    private fun scheduleAggressiveJobs() {
        // 积极模式：高频率任务
        val jobScheduler = context.getSystemService(Context.JOB_SCHEDULER_SERVICE) as JobScheduler
        
        // 快速任务
        val quickJobInfo = JobInfo.Builder(2003, ComponentName(context, KeepAliveJobService::class.java))
            .setMinimumLatency(10 * 1000) // 10秒最小延迟
            .setOverrideDeadline(30 * 1000) // 30秒最大延迟
            .setRequiredNetworkType(JobInfo.NETWORK_TYPE_ANY)
            .setRequiresCharging(false)
            .setRequiresDeviceIdle(false)
            .setPersisted(true)
            .build()
        
        jobScheduler.schedule(quickJobInfo)
        
        // 同时调度其他任务
        JobSchedulerHelper.scheduleNetworkJob(context)
        JobSchedulerHelper.scheduleChargingJob(context)
    }
    
    private fun getBatteryLevel(): Int {
        val batteryManager = context.getSystemService(Context.BATTERY_SERVICE) as BatteryManager
        return batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CAPACITY)
    }
    
    private fun isDeviceCharging(): Boolean {
        val batteryManager = context.getSystemService(Context.BATTERY_SERVICE) as BatteryManager
        return batteryManager.isCharging
    }
    
    private fun isNetworkAvailable(): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            capabilities?.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) == true
        } else {
            @Suppress("DEPRECATION")
            connectivityManager.activeNetworkInfo?.isConnected == true
        }
    }
    
    private fun getMemoryUsagePercent(): Int {
        val runtime = Runtime.getRuntime()
        val usedMemory = runtime.totalMemory() - runtime.freeMemory()
        val maxMemory = runtime.maxMemory()
        return (usedMemory.toDouble() / maxMemory * 100).toInt()
    }
    
    enum class SchedulingStrategy {
        POWER_SAVING,    // 节能模式
        LIGHTWEIGHT,     // 轻量模式
        BALANCED,        // 平衡模式
        AGGRESSIVE       // 积极模式
    }
}
```

---

## 3. 使用示例

### 3.1 在Application中初始化

```kotlin
class KeepAliveApplication : Application() {
    
    override fun onCreate() {
        super.onCreate()
        
        // 启动JobScheduler保活
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            initJobScheduler()
        }
    }
    
    private fun initJobScheduler() {
        // 启动智能调度管理器
        val smartManager = SmartJobSchedulerManager(this)
        smartManager.startSmartScheduling()
        
        // 也可以直接使用基础调度
        // JobSchedulerHelper.scheduleKeepAliveJob(this)
        // JobSchedulerHelper.schedulePeriodicJob(this)
    }
}
```

### 3.2 在Activity中监控状态

```kotlin
class MainActivity : AppCompatActivity() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)
        
        // 显示JobScheduler状态
        showJobSchedulerStatus()
    }
    
    private fun showJobSchedulerStatus() {
        val status = JobSchedulerHelper.getJobStatus(this)
        findViewById<TextView>(R.id.tv_job_status).text = status
    }
}
```

JobScheduler是Android推荐的后台任务调度方案，它能够智能地根据系统状态调度任务执行，是现代Android应用保活的重要组成部分。
