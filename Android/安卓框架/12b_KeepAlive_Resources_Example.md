# Android应用保活 - 资源文件配置示例

## 1. 认证器配置 (res/xml/authenticator.xml)

```xml
<?xml version="1.0" encoding="utf-8"?>
<account-authenticator xmlns:android="http://schemas.android.com/apk/res/android"
    android:accountType="com.example.keepalive.account"
    android:icon="@mipmap/ic_launcher"
    android:smallIcon="@mipmap/ic_launcher"
    android:label="@string/account_label" />
```

## 2. 同步适配器配置 (res/xml/sync_adapter.xml)

```xml
<?xml version="1.0" encoding="utf-8"?>
<sync-adapter xmlns:android="http://schemas.android.com/apk/res/android"
    android:contentAuthority="com.example.keepalive.provider"
    android:accountType="com.example.keepalive.account"
    android:userVisible="false"
    android:supportsUploading="false"
    android:allowParallelSyncs="false"
    android:isAlwaysSyncable="true" />
```

## 3. 网络安全配置 (res/xml/network_security_config.xml)

```xml
<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="true">your-server.com</domain>
        <domain includeSubdomains="true">localhost</domain>
    </domain-config>
</network-security-config>
```

## 4. 字符串资源 (res/values/strings.xml)

```xml
<resources>
    <string name="app_name">KeepAlive Demo</string>
    
    <!-- 账户相关 -->
    <string name="account_label">KeepAlive Account</string>
    <string name="account_type">com.example.keepalive.account</string>
    
    <!-- 通知相关 -->
    <string name="notification_channel_name">应用保活服务</string>
    <string name="notification_channel_description">保持应用在后台运行</string>
    <string name="notification_title">应用正在后台运行</string>
    <string name="notification_content">点击返回应用</string>
    
    <!-- 权限申请相关 -->
    <string name="permission_battery_title">电池优化设置</string>
    <string name="permission_battery_message">为了确保应用正常运行，请允许应用忽略电池优化</string>
    <string name="permission_notification_title">通知权限</string>
    <string name="permission_notification_message">应用需要通知权限来显示运行状态</string>
    
    <!-- 白名单引导相关 -->
    <string name="whitelist_guide_title">应用保活设置</string>
    <string name="whitelist_guide_button_positive">去设置</string>
    <string name="whitelist_guide_button_negative">取消</string>
    
    <!-- 华为设备引导 -->
    <string name="whitelist_guide_huawei">为了确保应用正常运行，请将本应用加入以下白名单：\n1. 设置 → 电池 → 启动管理 → 手动管理\n2. 设置 → 应用 → 权限管理 → 自启动管理\n3. 手机管家 → 电池管理 → 受保护应用</string>
    
    <!-- 小米设备引导 -->
    <string name="whitelist_guide_xiaomi">为了确保应用正常运行，请进行以下设置：\n1. 设置 → 应用设置 → 授权管理 → 自启动管理\n2. 设置 → 电量和性能 → 应用配置 → 无限制\n3. 安全中心 → 电量 → 应用智能省电 → 无限制</string>
    
    <!-- OPPO设备引导 -->
    <string name="whitelist_guide_oppo">为了确保应用正常运行，请进行以下设置：\n1. 设置 → 电池 → 耗电保护 → 允许后台运行\n2. 设置 → 应用管理 → 自启动管理\n3. 手机管家 → 权限隐私 → 自启动管理</string>
    
    <!-- VIVO设备引导 -->
    <string name="whitelist_guide_vivo">为了确保应用正常运行，请进行以下设置：\n1. 设置 → 电池 → 后台耗电管理 → 允许后台高耗电\n2. 设置 → 更多设置 → 权限管理 → 自启动\n3. i管家 → 应用管理 → 自启动管理</string>
    
    <!-- 通用设备引导 -->
    <string name="whitelist_guide_general">为了确保应用正常运行，请将本应用加入系统白名单，允许自启动和后台运行。</string>
</resources>
```

## 5. 样式资源 (res/values/styles.xml)

```xml
<resources>
    <!-- 应用主题 -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>

    <!-- 透明Activity主题 -->
    <style name="TransparentTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style>
</resources>
```

## 6. 颜色资源 (res/values/colors.xml)

```xml
<resources>
    <color name="colorPrimary">#3F51B5</color>
    <color name="colorPrimaryDark">#303F9F</color>
    <color name="colorAccent">#FF4081</color>
    
    <!-- 通知相关颜色 -->
    <color name="notification_color">#3F51B5</color>
</resources>
```

## 7. 布局文件示例 (res/layout/activity_main.xml)

```xml
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="应用保活Demo"
        android:textSize="24sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="32dp" />

    <Button
        android:id="@+id/btn_start_keep_alive"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="启动保活机制"
        android:layout_marginBottom="16dp" />

    <Button
        android:id="@+id/btn_stop_keep_alive"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="停止保活机制"
        android:layout_marginBottom="16dp" />

    <Button
        android:id="@+id/btn_check_permissions"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="检查权限状态"
        android:layout_marginBottom="16dp" />

    <Button
        android:id="@+id/btn_request_permissions"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="申请保活权限"
        android:layout_marginBottom="16dp" />

    <Button
        android:id="@+id/btn_whitelist_guide"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="白名单设置引导"
        android:layout_marginBottom="16dp" />

    <Button
        android:id="@+id/btn_view_metrics"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="查看监控数据"
        android:layout_marginBottom="16dp" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginTop="16dp">

        <TextView
            android:id="@+id/tv_status"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="状态信息将在这里显示..."
            android:textSize="14sp"
            android:fontFamily="monospace"
            android:background="#f5f5f5"
            android:padding="12dp" />

    </ScrollView>

</LinearLayout>
```

## 8. Gradle配置示例 (app/build.gradle)

```gradle
android {
    compileSdkVersion 33
    buildToolsVersion "33.0.0"

    defaultConfig {
        applicationId "com.example.keepalive"
        minSdkVersion 21
        targetSdkVersion 33
        versionCode 1
        versionName "1.0"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    
    // WorkManager
    implementation 'androidx.work:work-runtime:2.8.1'
    implementation 'androidx.work:work-runtime-ktx:2.8.1'
    
    // 网络库
    implementation 'com.squareup.okhttp3:okhttp:4.10.0'
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    
    // 日志库
    implementation 'com.jakewharton.timber:timber:5.0.1'
    
    // 测试依赖
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
}
```

## 9. ProGuard配置 (proguard-rules.pro)

```proguard
# 保活相关类不混淆
-keep class com.example.keepalive.service.** { *; }
-keep class com.example.keepalive.receiver.** { *; }
-keep class com.example.keepalive.sync.** { *; }
-keep class com.example.keepalive.job.** { *; }
-keep class com.example.keepalive.manager.** { *; }

# WorkManager
-keep class androidx.work.** { *; }
-keep class * extends androidx.work.Worker
-keep class * extends androidx.work.InputMerger

# OkHttp
-dontwarn okhttp3.**
-dontwarn okio.**
-dontwarn javax.annotation.**

# 保持native方法
-keepclasseswithmembernames class * {
    native <methods>;
}

# 保持Parcelable
-keep class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}
```

## 10. 使用说明

### 10.1 集成步骤

1. **复制配置文件**: 将上述配置文件复制到对应的目录
2. **修改包名**: 将所有的`com.example.keepalive`替换为你的实际包名
3. **添加依赖**: 在`build.gradle`中添加必要的依赖
4. **实现代码**: 根据主文档实现相应的Java/Kotlin代码
5. **测试验证**: 在不同设备和Android版本上测试保活效果

### 10.2 权限说明

- `FOREGROUND_SERVICE`: 前台服务必需
- `WAKE_LOCK`: 保持CPU唤醒
- `RECEIVE_BOOT_COMPLETED`: 开机自启动
- `REQUEST_IGNORE_BATTERY_OPTIMIZATIONS`: 忽略电池优化
- `POST_NOTIFICATIONS`: Android 13+通知权限

### 10.3 注意事项

1. **权限申请**: 某些权限需要用户手动授予
2. **厂商适配**: 不同厂商可能需要额外的权限或设置
3. **版本兼容**: 注意Android版本兼容性
4. **性能影响**: 合理使用保活机制，避免影响设备性能
5. **用户体验**: 保活应该是透明的，不影响用户正常使用

这些配置文件为Android应用保活提供了完整的基础设施支持，结合主文档中的代码实现，可以构建一个完整的保活解决方案。
