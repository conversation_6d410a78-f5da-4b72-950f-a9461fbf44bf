package com.example.feature_profile;

import com.example.common.IUserService;
import com.example.common.ServiceManager;

/**
 * 个人资料页面，它依赖 IUserService
 */
public class ProfileActivity {
    public void showUserProfile() {
        // 通过 ServiceManager 获取服务，而不是直接 new LoginService()
        IUserService userService = ServiceManager.getService(IUserService.class);

        if (userService != null && userService.isLogin()) {
            String userId = userService.getUserId();
            System.out.println("Showing profile for User ID: " + userId);
            // ... 加载并显示用户详细信息 ...
        } else {
            System.out.println("User not logged in. Redirecting to login page.");
            // 在真实项目中，这里会通过路由跳转到登录页
        }
    }
}
