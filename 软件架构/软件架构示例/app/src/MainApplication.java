package com.example.app;

import com.example.common.IUserService;
import com.example.common.ServiceManager;
import com.example.feature_login.LoginService;
import com.example.feature_profile.ProfileActivity;

/**
 * App 的主入口，负责初始化和组装
 */
public class MainApplication {
    public static void main(String[] args) {
        // 1. App 启动时，初始化并注册所有模块的服务
        System.out.println("--- App is starting, registering services... ---");
        LoginService loginService = new LoginService();
        ServiceManager.registerService(IUserService.class, loginService);
        System.out.println("UserService registered.");

        // 2. 模拟进入个人资料页的场景
        System.out.println("\n--- Navigating to Profile page... ---");
        ProfileActivity profileActivity = new ProfileActivity();
        profileActivity.showUserProfile(); // 此时用户未登录

        // 3. 模拟用户登录
        System.out.println("\n--- Simulating user login... ---");
        loginService.login("admin", "123456");

        // 4. 再次模拟进入个人资料页
        System.out.println("\n--- Navigating to Profile page again... ---");
        profileActivity.showUserProfile(); // 此时用户已登录
    }
}
